"""
LLM Handler Module for VQA Dataset Enhancement System
Handles Llama 3.1 8B Instruct model integration with iterative prompting strategies.
"""

import torch
import logging
from typing import Optional, List, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from config import MODEL_NAME, DEVICE, MAX_LENGTH, TEMPERATURE_LEVELS, PROMPTS

logger = logging.getLogger(__name__)

class LLMHandler:
    """Handles LLM interactions with iterative prompting strategy."""

    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = DEVICE
        self.is_available = False

    def initialize_model(self) -> bool:
        """Initialize the Llama 3.1 8B Instruct model."""
        try:
            logger.info(f"Initializing {MODEL_NAME} on {self.device}")

            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Load model with appropriate settings
            model_kwargs = {
                "torch_dtype": torch.float16 if self.device == "cuda" else torch.float32,
                "device_map": "auto" if self.device == "cuda" else None,
                "trust_remote_code": True
            }

            if self.device == "cuda":
                model_kwargs["low_cpu_mem_usage"] = True

            self.model = AutoModelForCausalLM.from_pretrained(MODEL_NAME, **model_kwargs)

            # Create pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                torch_dtype=torch.float16 if self.device == "cuda" else torch.float32,
            )

            self.is_available = True
            logger.info("LLM model initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize LLM model: {str(e)}")
            self.is_available = False
            return False

    def generate_keywords_iterative(self, question: str, korean_term: str) -> Tuple[Optional[str], List[str]]:
        """
        Generate keywords using iterative prompting strategy.

        Args:
            question: The VQA question
            korean_term: The Korean cultural term

        Returns:
            Tuple of (final_keyword, all_attempts) where all_attempts contains raw responses
        """
        if not self.is_available:
            logger.warning("     ⚠️  LLM model not available, skipping keyword generation")
            return None, []

        logger.info(f"     🧠 Starting LLM keyword generation for '{korean_term}'")
        all_attempts = []

        for attempt_num in range(3):
            try:
                temperature = TEMPERATURE_LEVELS[attempt_num]
                prompt_key = f"attempt_{attempt_num + 1}"

                logger.info(f"     🎯 LLM Attempt {attempt_num + 1}/3 (temperature: {temperature})")
                logger.info(f"     📝 Using prompt strategy: {prompt_key}")

                # Generate response
                logger.info(f"     ⏳ Generating response...")
                response = self._generate_response(
                    question=question,
                    korean_term=korean_term,
                    prompt_template=PROMPTS[prompt_key],
                    temperature=temperature
                )

                all_attempts.append(response)

                if response and response.strip().upper() != "NONE":
                    logger.info(f"     ✅ LLM attempt {attempt_num + 1} generated: {response[:80]}...")
                    logger.info(f"     🎉 Found valid response on attempt {attempt_num + 1}, stopping iteration")
                    return response, all_attempts
                else:
                    logger.info(f"     ❌ LLM attempt {attempt_num + 1} returned no valid keyword")
                    if attempt_num < 2:
                        logger.info(f"     🔄 Trying next attempt with higher creativity...")

            except Exception as e:
                logger.error(f"     💥 Error in LLM attempt {attempt_num + 1}: {str(e)}")
                all_attempts.append(f"ERROR: {str(e)}")

        logger.warning(f"     ❌ All 3 LLM attempts failed for '{korean_term}'")
        return None, all_attempts

    def _generate_response(self, question: str, korean_term: str, prompt_template: str, temperature: float) -> Optional[str]:
        """Generate response using the LLM."""
        import time

        try:
            # Format the prompt
            logger.debug(f"     📋 Formatting prompt template...")
            prompt = prompt_template.format(question=question, korean_term=korean_term)
            logger.debug(f"     📏 Prompt length: {len(prompt)} characters")

            # Generate response
            logger.info(f"     🚀 Starting LLM inference...")
            start_time = time.time()

            outputs = self.pipeline(
                prompt,
                max_new_tokens=MAX_LENGTH,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                return_full_text=False,
                num_return_sequences=1
            )

            end_time = time.time()
            inference_time = end_time - start_time
            logger.info(f"     ⏱️  LLM inference completed in {inference_time:.2f} seconds")

            if outputs and len(outputs) > 0:
                response = outputs[0]['generated_text'].strip()
                logger.info(f"     📤 Generated {len(response)} characters of response")
                return response
            else:
                logger.warning(f"     ⚠️  LLM returned empty output")

            return None

        except Exception as e:
            logger.error(f"     💥 Error generating LLM response: {str(e)}")
            return None

    def test_model(self) -> bool:
        """Test if the model is working correctly."""
        if not self.is_available:
            return False

        try:
            test_response = self._generate_response(
                question="What is this traditional Korean palace?",
                korean_term="경복궁",
                prompt_template=PROMPTS["attempt_1"],
                temperature=0.7
            )

            return test_response is not None and len(test_response) > 0

        except Exception as e:
            logger.error(f"Model test failed: {str(e)}")
            return False

    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        info = {
            "model_name": MODEL_NAME,
            "device": self.device,
            "is_available": self.is_available,
            "cuda_available": torch.cuda.is_available(),
            "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
        }

        if self.is_available and hasattr(self.model, 'config'):
            info.update({
                "model_type": getattr(self.model.config, 'model_type', 'unknown'),
                "vocab_size": getattr(self.model.config, 'vocab_size', 'unknown'),
                "hidden_size": getattr(self.model.config, 'hidden_size', 'unknown')
            })

        return info

    def cleanup(self):
        """Clean up model resources."""
        try:
            if self.model is not None:
                del self.model
            if self.tokenizer is not None:
                del self.tokenizer
            if self.pipeline is not None:
                del self.pipeline

            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            self.is_available = False
            logger.info("LLM model resources cleaned up")

        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.cleanup()
