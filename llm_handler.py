"""
LLM Handler Module for VQA Dataset Enhancement System
Handles Llama 3.1 8B Instruct model integration with iterative prompting strategies.
"""

import torch
import logging
import os
from typing import Optional, List, Tuple
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from config import MOD<PERSON>_NAME, DEVICE, MAX_LENGTH, TEMPERATURE_LEVELS, PROMPTS

logger = logging.getLogger(__name__)

class LLMHandler:
    """Handles LLM interactions with iterative prompting strategy and multi-GPU support."""

    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.pipeline = None
        self.device = DEVICE
        self.is_available = False
        self.num_gpus = 0
        self.gpu_devices = []
        self.use_multi_gpu = False

    def _detect_gpus(self):
        """Detect available GPUs and configure multi-GPU setup."""
        if torch.cuda.is_available():
            self.num_gpus = torch.cuda.device_count()
            self.gpu_devices = list(range(self.num_gpus))

            # Check if CUDA_VISIBLE_DEVICES is set
            cuda_visible = os.environ.get('CUDA_VISIBLE_DEVICES')
            if cuda_visible:
                try:
                    visible_devices = [int(x) for x in cuda_visible.split(',') if x.strip()]
                    self.gpu_devices = visible_devices
                    self.num_gpus = len(visible_devices)
                except:
                    pass

            self.use_multi_gpu = self.num_gpus > 1

            logger.info(f"🚀 Detected {self.num_gpus} GPU(s): {self.gpu_devices}")
            if self.use_multi_gpu:
                logger.info(f"🔥 Multi-GPU mode enabled with {self.num_gpus} GPUs")
            else:
                logger.info(f"📱 Single GPU mode")
        else:
            logger.info("💻 CPU mode - no GPUs available")
            self.num_gpus = 0
            self.use_multi_gpu = False

    def initialize_model(self) -> bool:
        """Initialize the Llama 3.1 8B Instruct model with multi-GPU support."""
        try:
            # Detect available GPUs
            self._detect_gpus()

            logger.info(f"🤖 Initializing {MODEL_NAME}")
            if self.use_multi_gpu:
                logger.info(f"🔥 Using multi-GPU setup with {self.num_gpus} GPUs")

            # Load tokenizer
            logger.info("📝 Loading tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            # Configure model loading based on GPU availability
            if self.use_multi_gpu:
                # Multi-GPU setup with device_map
                logger.info(f"🔥 Loading model across {self.num_gpus} GPUs...")
                model_kwargs = {
                    "torch_dtype": torch.float16,
                    "device_map": "auto",  # Automatically distribute across GPUs
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True,
                    "max_memory": self._get_max_memory_config()
                }
            elif self.num_gpus == 1:
                # Single GPU setup
                logger.info("📱 Loading model on single GPU...")
                model_kwargs = {
                    "torch_dtype": torch.float16,
                    "device_map": {"": 0},  # Load on GPU 0
                    "trust_remote_code": True,
                    "low_cpu_mem_usage": True
                }
            else:
                # CPU setup
                logger.info("💻 Loading model on CPU...")
                model_kwargs = {
                    "torch_dtype": torch.float32,
                    "device_map": None,
                    "trust_remote_code": True
                }

            self.model = AutoModelForCausalLM.from_pretrained(MODEL_NAME, **model_kwargs)

            # Create pipeline with appropriate device setting
            if self.use_multi_gpu:
                # For multi-GPU, let the model handle device placement
                device_setting = None  # Let device_map handle it
            elif self.num_gpus >= 1:
                device_setting = 0  # Single GPU
            else:
                device_setting = -1  # CPU

            logger.info("🔧 Creating inference pipeline...")
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=device_setting,
                torch_dtype=torch.float16 if self.num_gpus > 0 else torch.float32,
            )

            self.is_available = True
            logger.info("✅ LLM model initialized successfully")

            # Print memory usage for each GPU
            if self.num_gpus > 0:
                self._log_gpu_memory()

            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize LLM model: {str(e)}")
            self.is_available = False
            return False

    def _get_max_memory_config(self) -> dict:
        """Configure maximum memory usage per GPU for optimal distribution."""
        max_memory = {}

        if self.num_gpus > 0:
            for gpu_id in self.gpu_devices:
                # Get GPU memory info
                gpu_memory = torch.cuda.get_device_properties(gpu_id).total_memory
                # Reserve 2GB for other processes, use 90% of remaining
                usable_memory = int((gpu_memory - 2 * 1024**3) * 0.9)
                max_memory[gpu_id] = f"{usable_memory // (1024**3)}GB"
                logger.info(f"   GPU {gpu_id}: {max_memory[gpu_id]} allocated")

        return max_memory

    def _log_gpu_memory(self):
        """Log current GPU memory usage."""
        for gpu_id in self.gpu_devices:
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated(gpu_id) / 1024**3
                cached = torch.cuda.memory_reserved(gpu_id) / 1024**3
                total = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
                logger.info(f"   GPU {gpu_id}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total")

    def generate_keywords_iterative(self, question: str, korean_term: str) -> Tuple[Optional[str], List[str]]:
        """
        Generate keywords using iterative prompting strategy.

        Args:
            question: The VQA question
            korean_term: The Korean cultural term

        Returns:
            Tuple of (final_keyword, all_attempts) where all_attempts contains raw responses
        """
        if not self.is_available:
            logger.warning("     ⚠️  LLM model not available, skipping keyword generation")
            return None, []

        logger.info(f"     🧠 Starting LLM keyword generation for '{korean_term}'")
        all_attempts = []

        for attempt_num in range(3):
            try:
                temperature = TEMPERATURE_LEVELS[attempt_num]
                prompt_key = f"attempt_{attempt_num + 1}"

                logger.info(f"     🎯 LLM Attempt {attempt_num + 1}/3 (temperature: {temperature})")
                logger.info(f"     📝 Using prompt strategy: {prompt_key}")

                # Generate response
                logger.info(f"     ⏳ Generating response...")
                response = self._generate_response(
                    question=question,
                    korean_term=korean_term,
                    prompt_template=PROMPTS[prompt_key],
                    temperature=temperature
                )

                all_attempts.append(response)

                if response and response.strip().upper() != "NONE":
                    logger.info(f"     ✅ LLM attempt {attempt_num + 1} generated: {response[:80]}...")
                    logger.info(f"     🎉 Found valid response on attempt {attempt_num + 1}, stopping iteration")
                    return response, all_attempts
                else:
                    logger.info(f"     ❌ LLM attempt {attempt_num + 1} returned no valid keyword")
                    if attempt_num < 2:
                        logger.info(f"     🔄 Trying next attempt with higher creativity...")

            except Exception as e:
                logger.error(f"     💥 Error in LLM attempt {attempt_num + 1}: {str(e)}")
                all_attempts.append(f"ERROR: {str(e)}")

        logger.warning(f"     ❌ All 3 LLM attempts failed for '{korean_term}'")
        return None, all_attempts

    def _generate_response(self, question: str, korean_term: str, prompt_template: str, temperature: float) -> Optional[str]:
        """Generate response using the LLM."""
        import time

        try:
            # Format the prompt
            logger.debug(f"     📋 Formatting prompt template...")
            prompt = prompt_template.format(question=question, korean_term=korean_term)
            logger.debug(f"     📏 Prompt length: {len(prompt)} characters")

            # Generate response
            logger.info(f"     🚀 Starting LLM inference...")
            start_time = time.time()

            outputs = self.pipeline(
                prompt,
                max_new_tokens=MAX_LENGTH,
                temperature=temperature,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                return_full_text=False,
                num_return_sequences=1
            )

            end_time = time.time()
            inference_time = end_time - start_time
            logger.info(f"     ⏱️  LLM inference completed in {inference_time:.2f} seconds")

            if outputs and len(outputs) > 0:
                response = outputs[0]['generated_text'].strip()
                logger.info(f"     📤 Generated {len(response)} characters of response")
                return response
            else:
                logger.warning(f"     ⚠️  LLM returned empty output")

            return None

        except Exception as e:
            logger.error(f"     💥 Error generating LLM response: {str(e)}")
            return None

    def test_model(self) -> bool:
        """Test if the model is working correctly."""
        if not self.is_available:
            return False

        try:
            test_response = self._generate_response(
                question="What is this traditional Korean palace?",
                korean_term="경복궁",
                prompt_template=PROMPTS["attempt_1"],
                temperature=0.7
            )

            return test_response is not None and len(test_response) > 0

        except Exception as e:
            logger.error(f"Model test failed: {str(e)}")
            return False

    def get_model_info(self) -> dict:
        """Get information about the loaded model."""
        info = {
            "model_name": MODEL_NAME,
            "device": self.device,
            "is_available": self.is_available,
            "cuda_available": torch.cuda.is_available(),
            "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "num_gpus": self.num_gpus,
            "gpu_devices": self.gpu_devices,
            "use_multi_gpu": self.use_multi_gpu
        }

        if self.is_available and hasattr(self.model, 'config'):
            info.update({
                "model_type": getattr(self.model.config, 'model_type', 'unknown'),
                "vocab_size": getattr(self.model.config, 'vocab_size', 'unknown'),
                "hidden_size": getattr(self.model.config, 'hidden_size', 'unknown')
            })

        # Add GPU memory info
        if self.num_gpus > 0:
            gpu_memory_info = {}
            for gpu_id in self.gpu_devices:
                if torch.cuda.is_available():
                    total_memory = torch.cuda.get_device_properties(gpu_id).total_memory / 1024**3
                    gpu_memory_info[f"gpu_{gpu_id}"] = f"{total_memory:.1f}GB"
            info["gpu_memory"] = gpu_memory_info

        return info

    def cleanup(self):
        """Clean up model resources."""
        try:
            if self.model is not None:
                del self.model
            if self.tokenizer is not None:
                del self.tokenizer
            if self.pipeline is not None:
                del self.pipeline

            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            self.is_available = False
            logger.info("LLM model resources cleaned up")

        except Exception as e:
            logger.error(f"Error during cleanup: {str(e)}")

    def __del__(self):
        """Destructor to ensure cleanup."""
        self.cleanup()
