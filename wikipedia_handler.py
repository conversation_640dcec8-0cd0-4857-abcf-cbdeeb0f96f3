"""
Wikipedia Handler Module for VQA Dataset Enhancement System
Manages Wikipedia API interactions and content extraction with advanced search strategies.
"""

import wikipedia
import logging
import re
import requests
from typing import Optional, Tuple, List, Dict
from config import WIKIPEDIA_LANGUAGE, MAX_SENTENCES, TIMEOUT_SECONDS

logger = logging.getLogger(__name__)

class WikipediaHandler:
    """Handles Wikipedia page retrieval and content extraction with advanced search strategies."""

    def __init__(self):
        wikipedia.set_lang(WIKIPEDIA_LANGUAGE)
        wikipedia.set_rate_limiting(True)

        # Common Korean romanization variants and alternative names
        self.romanization_variants = {
            # Common substitutions
            'eo': ['o', 'u'],
            'eu': ['u', 'e'],
            'ae': ['e', 'a'],
            'oe': ['e', 'we'],
            'ui': ['i', 'wi'],
            'gw': ['gu', 'kw'],
            'kw': ['gw', 'qu'],
            'j': ['ch'],
            'ch': ['j'],
            'g': ['k'],
            'k': ['g'],
            'b': ['p'],
            'p': ['b'],
            'd': ['t'],
            't': ['d']
        }

        # Common Korean place/term mappings
        self.korean_mappings = {
            '궁': ['Palace', 'Gung'],
            '사': ['Temple', 'Sa'],
            '산': ['Mountain', 'San'],
            '강': ['River', 'Gang'],
            '교': ['Bridge', 'Gyo'],
            '문': ['Gate', 'Mun'],
            '탑': ['Tower', 'Pagoda', 'Tap'],
            '공원': ['Park'],
            '시장': ['Market'],
            '대학교': ['University'],
            '병원': ['Hospital'],
            '역': ['Station'],
            '구': ['District', 'Gu'],
            '동': ['Dong', 'District'],
            '로': ['Road', 'Ro'],
            '길': ['Street', 'Gil']
        }

    def get_knowledge_point(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Retrieve knowledge point and source URL from Wikipedia using advanced search strategies.

        Args:
            keyword: The search term for Wikipedia

        Returns:
            Tuple of (knowledge_point, source_url) or (None, None) if not found
        """
        if not keyword:
            logger.warning(f"     ⚠️  Empty keyword provided to Wikipedia search")
            return None, None

        try:
            logger.info(f"     🔍 Advanced Wikipedia search for: '{keyword}'")

            # Try multiple search strategies in order of preference
            search_strategies = [
                ("Direct English search", lambda: self._search_english_wikipedia(keyword)),
                ("Korean Wikipedia + translation", lambda: self._search_korean_wikipedia(keyword)),
                ("Romanization variants", lambda: self._search_romanization_variants(keyword)),
                ("Related terms search", lambda: self._search_related_terms(keyword)),
                ("Fuzzy matching", lambda: self._search_fuzzy_matching(keyword))
            ]

            for strategy_name, search_func in search_strategies:
                logger.info(f"     🎯 Trying strategy: {strategy_name}")

                try:
                    page = search_func()
                    if page:
                        logger.info(f"     ✅ Found page with {strategy_name}: '{page.title}'")

                        # Extract and validate content
                        knowledge_point, source_url = self._process_found_page(page)
                        if knowledge_point:
                            return knowledge_point, source_url
                        else:
                            logger.info(f"     ⚠️  Page found but content not suitable, trying next strategy...")
                            continue
                    else:
                        logger.info(f"     ❌ No page found with {strategy_name}")

                except Exception as e:
                    logger.warning(f"     ⚠️  Strategy '{strategy_name}' failed: {str(e)}")
                    continue

            logger.warning(f"     ❌ All search strategies failed for: '{keyword}'")
            return None, None

        except Exception as e:
            logger.error(f"     💥 Error in advanced Wikipedia search for '{keyword}': {str(e)}")
            return None, None

    def _process_found_page(self, page) -> Tuple[Optional[str], Optional[str]]:
        """Process a found Wikipedia page to extract and validate content."""
        try:
            # Extract content
            logger.info(f"       📝 Extracting content from: {page.title}")
            knowledge_point = self._extract_knowledge_point(page)
            if not knowledge_point:
                logger.warning(f"       ❌ No valid content extracted from page: {page.title}")
                return None, None

            logger.info(f"       📏 Extracted {len(knowledge_point)} characters of content")

            # Validate Korean cultural relevance
            logger.info(f"       🏛️  Validating Korean cultural relevance...")
            if not self._is_korean_cultural_content(knowledge_point, page.title):
                logger.warning(f"       ❌ Content not relevant to Korean culture: {page.title}")
                return None, None

            source_url = page.url
            logger.info(f"       🎉 Successfully validated content from: {page.title}")
            logger.info(f"       🔗 Source URL: {source_url}")

            return knowledge_point, source_url

        except Exception as e:
            logger.error(f"       💥 Error processing page: {str(e)}")
            return None, None

    def _search_english_wikipedia(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Search English Wikipedia directly (original method)."""
        return self._get_wikipedia_page(keyword)

    def _search_korean_wikipedia(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Search Korean Wikipedia and translate results to English."""
        try:
            logger.info(f"         🇰🇷 Searching Korean Wikipedia...")

            # Set to Korean Wikipedia
            original_lang = wikipedia.get_lang()
            wikipedia.set_lang('ko')

            try:
                # Search Korean Wikipedia
                ko_results = wikipedia.search(keyword, results=5)
                logger.info(f"         📋 Korean Wikipedia results: {ko_results}")

                for ko_title in ko_results:
                    try:
                        # Get Korean page
                        ko_page = wikipedia.page(ko_title, auto_suggest=False)

                        # Try to find English equivalent
                        en_page = self._find_english_equivalent(ko_page)
                        if en_page:
                            logger.info(f"         ✅ Found English equivalent: {en_page.title}")
                            return en_page

                    except Exception as e:
                        logger.debug(f"         ⚠️  Failed to process Korean result '{ko_title}': {str(e)}")
                        continue

            finally:
                # Restore original language
                wikipedia.set_lang(original_lang)

            return None

        except Exception as e:
            logger.warning(f"         ❌ Korean Wikipedia search failed: {str(e)}")
            return None

    def _find_english_equivalent(self, ko_page) -> Optional[wikipedia.WikipediaPage]:
        """Find English equivalent of a Korean Wikipedia page."""
        try:
            # Look for interlanguage links in the page content
            # This is a simplified approach - in practice, you'd use the Wikipedia API
            # to get proper interlanguage links

            # Try searching English Wikipedia with the Korean page title
            wikipedia.set_lang('en')

            # Extract potential English terms from Korean page
            ko_title = ko_page.title

            # Try direct search with Korean title
            try:
                en_page = wikipedia.page(ko_title, auto_suggest=True)
                return en_page
            except:
                pass

            # Try searching for the Korean title
            search_results = wikipedia.search(ko_title, results=3)
            for result in search_results:
                try:
                    en_page = wikipedia.page(result, auto_suggest=False)
                    # Basic relevance check
                    if self._is_korean_cultural_content(en_page.content[:500], en_page.title):
                        return en_page
                except:
                    continue

            return None

        except Exception as e:
            logger.debug(f"         ⚠️  Error finding English equivalent: {str(e)}")
            return None

    def _search_romanization_variants(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Search using different romanization variants."""
        try:
            logger.info(f"         🔤 Trying romanization variants...")

            variants = self._generate_romanization_variants(keyword)
            logger.info(f"         📝 Generated variants: {variants[:5]}...")  # Show first 5

            for variant in variants:
                if variant.lower() != keyword.lower():  # Skip original
                    try:
                        page = self._get_wikipedia_page(variant)
                        if page:
                            logger.info(f"         ✅ Found with variant '{variant}': {page.title}")
                            return page
                    except:
                        continue

            return None

        except Exception as e:
            logger.warning(f"         ❌ Romanization variants search failed: {str(e)}")
            return None

    def _generate_romanization_variants(self, keyword: str) -> List[str]:
        """Generate different romanization variants of a keyword."""
        variants = [keyword]

        # Generate variants based on common romanization differences
        for original, replacements in self.romanization_variants.items():
            for replacement in replacements:
                if original in keyword.lower():
                    new_variant = keyword.lower().replace(original, replacement)
                    variants.append(new_variant.title())
                    variants.append(new_variant.capitalize())
                    variants.append(new_variant.upper())

        # Remove duplicates while preserving order
        seen = set()
        unique_variants = []
        for variant in variants:
            if variant not in seen:
                seen.add(variant)
                unique_variants.append(variant)

        return unique_variants

    def _search_related_terms(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Search using related terms and broader concepts."""
        try:
            logger.info(f"         🔗 Trying related terms...")

            related_terms = self._generate_related_terms(keyword)
            logger.info(f"         📝 Related terms: {related_terms}")

            for term in related_terms:
                try:
                    page = self._get_wikipedia_page(term)
                    if page:
                        logger.info(f"         ✅ Found with related term '{term}': {page.title}")
                        return page
                except:
                    continue

            return None

        except Exception as e:
            logger.warning(f"         ❌ Related terms search failed: {str(e)}")
            return None

    def _generate_related_terms(self, keyword: str) -> List[str]:
        """Generate related search terms."""
        related_terms = []

        # Add common Korean suffixes/prefixes
        for korean_part, english_parts in self.korean_mappings.items():
            if korean_part in keyword:
                base = keyword.replace(korean_part, '').strip()
                for english_part in english_parts:
                    related_terms.append(f"{base} {english_part}".strip())
                    related_terms.append(f"{english_part} {base}".strip())

        # Add variations with common Korean place indicators
        if any(indicator in keyword.lower() for indicator in ['palace', 'temple', 'mountain', 'bridge']):
            # Already has indicator, try without
            for indicator in ['palace', 'temple', 'mountain', 'bridge', 'tower', 'gate']:
                if indicator in keyword.lower():
                    base = keyword.lower().replace(indicator, '').strip()
                    if base:
                        related_terms.append(base.title())
        else:
            # Try adding common indicators
            for indicator in ['Palace', 'Temple', 'Mountain', 'Bridge', 'Tower', 'Gate']:
                related_terms.append(f"{keyword} {indicator}")

        # Remove empty strings and duplicates
        related_terms = [term for term in related_terms if term.strip()]
        return list(set(related_terms))

    def _search_fuzzy_matching(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Search using fuzzy matching on search results."""
        try:
            logger.info(f"         🎯 Trying fuzzy matching...")

            # Get more search results and try fuzzy matching
            search_results = wikipedia.search(keyword, results=10)
            logger.info(f"         📋 Search results for fuzzy matching: {search_results}")

            # Score results based on similarity
            scored_results = []
            for result in search_results:
                score = self._calculate_similarity(keyword.lower(), result.lower())
                scored_results.append((score, result))

            # Sort by score (highest first)
            scored_results.sort(reverse=True)

            # Try the best matches
            for score, result in scored_results[:3]:  # Try top 3
                if score > 0.3:  # Minimum similarity threshold
                    try:
                        page = wikipedia.page(result, auto_suggest=False)
                        logger.info(f"         ✅ Found with fuzzy match '{result}' (score: {score:.2f}): {page.title}")
                        return page
                    except:
                        continue

            return None

        except Exception as e:
            logger.warning(f"         ❌ Fuzzy matching search failed: {str(e)}")
            return None

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calculate simple similarity score between two strings."""
        # Simple Jaccard similarity based on character n-grams
        def get_ngrams(s, n=2):
            return set(s[i:i+n] for i in range(len(s)-n+1))

        ngrams1 = get_ngrams(str1)
        ngrams2 = get_ngrams(str2)

        if not ngrams1 and not ngrams2:
            return 1.0
        if not ngrams1 or not ngrams2:
            return 0.0

        intersection = len(ngrams1.intersection(ngrams2))
        union = len(ngrams1.union(ngrams2))

        return intersection / union if union > 0 else 0.0

    def _get_wikipedia_page(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Get Wikipedia page for the given keyword."""
        try:
            # First try exact match
            logger.debug(f"       🎯 Trying exact match for '{keyword}'...")
            try:
                page = wikipedia.page(keyword, auto_suggest=False)
                logger.info(f"       ✅ Exact match found: '{page.title}'")
                return page
            except wikipedia.DisambiguationError as e:
                # Handle disambiguation - try the first option
                if e.options:
                    logger.info(f"       🔀 Disambiguation found for '{keyword}', trying: {e.options[0]}")
                    try:
                        page = wikipedia.page(e.options[0], auto_suggest=False)
                        logger.info(f"       ✅ Disambiguation resolved: '{page.title}'")
                        return page
                    except:
                        logger.debug(f"       ❌ Disambiguation option failed")
                        pass
            except wikipedia.PageError:
                logger.debug(f"       ❌ Exact match failed - page not found")
                pass

            # Try with auto-suggest
            logger.debug(f"       🔍 Trying auto-suggest for '{keyword}'...")
            try:
                page = wikipedia.page(keyword, auto_suggest=True)
                logger.info(f"       ✅ Auto-suggest found: '{page.title}'")
                return page
            except wikipedia.DisambiguationError as e:
                if e.options:
                    logger.info(f"       🔀 Auto-suggest disambiguation for '{keyword}', trying: {e.options[0]}")
                    try:
                        page = wikipedia.page(e.options[0], auto_suggest=False)
                        logger.info(f"       ✅ Auto-suggest disambiguation resolved: '{page.title}'")
                        return page
                    except:
                        logger.debug(f"       ❌ Auto-suggest disambiguation failed")
                        pass
            except wikipedia.PageError:
                logger.debug(f"       ❌ Auto-suggest failed - page not found")
                pass

            # Try search and get first result
            logger.debug(f"       🔎 Trying search for '{keyword}'...")
            search_results = wikipedia.search(keyword, results=3)
            logger.info(f"       📋 Search returned {len(search_results)} results: {search_results}")

            for i, result in enumerate(search_results):
                try:
                    logger.debug(f"       🎯 Trying search result {i+1}: '{result}'")
                    page = wikipedia.page(result, auto_suggest=False)
                    logger.info(f"       ✅ Search result found: '{page.title}'")
                    return page
                except:
                    logger.debug(f"       ❌ Search result {i+1} failed")
                    continue

            logger.warning(f"       ❌ All Wikipedia search methods failed for '{keyword}'")
            return None

        except Exception as e:
            logger.error(f"       💥 Error getting Wikipedia page for '{keyword}': {str(e)}")
            return None

    def _extract_knowledge_point(self, page: wikipedia.WikipediaPage) -> Optional[str]:
        """Extract the first paragraph as knowledge point."""
        try:
            content = page.content
            if not content:
                return None

            # Get the first paragraph (before the first section)
            paragraphs = content.split('\n\n')
            first_paragraph = None

            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                # Skip empty paragraphs and section headers
                if paragraph and not paragraph.startswith('==') and len(paragraph) > 50:
                    first_paragraph = paragraph
                    break

            if not first_paragraph:
                return None

            # Clean the paragraph
            knowledge_point = self._clean_paragraph(first_paragraph)

            # Limit to MAX_SENTENCES if too long
            if knowledge_point:
                knowledge_point = self._limit_sentences(knowledge_point, MAX_SENTENCES)

            return knowledge_point

        except Exception as e:
            logger.error(f"Error extracting content from page: {str(e)}")
            return None

    def _clean_paragraph(self, paragraph: str) -> str:
        """Clean Wikipedia paragraph text."""
        # Remove Wikipedia markup
        paragraph = re.sub(r'\[\[([^\]]+)\]\]', r'\1', paragraph)  # Remove links
        paragraph = re.sub(r'\[([^\]]+)\]', '', paragraph)  # Remove references
        paragraph = re.sub(r'\{\{[^}]+\}\}', '', paragraph)  # Remove templates

        # Clean up whitespace
        paragraph = ' '.join(paragraph.split())

        return paragraph.strip()

    def _limit_sentences(self, text: str, max_sentences: int) -> str:
        """Limit text to maximum number of sentences."""
        # Split by sentence endings
        sentences = re.split(r'[.!?]+', text)

        # Keep only the first max_sentences
        if len(sentences) > max_sentences:
            # Take first max_sentences and add proper ending
            limited_sentences = sentences[:max_sentences]
            result = '. '.join(s.strip() for s in limited_sentences if s.strip())
            if result and not result.endswith('.'):
                result += '.'
            return result

        return text

    def _is_korean_cultural_content(self, content: str, title: str) -> bool:
        """Check if content is relevant to Korean culture with enhanced detection."""
        # Primary Korean indicators
        korean_indicators = [
            'korea', 'korean', 'seoul', 'busan', 'jeju', 'joseon', 'goryeo', 'silla',
            'baekje', 'goguryeo', 'hanbok', 'kimchi', 'taekwondo', 'hangul',
            'confucian', 'buddhist', 'temple', 'palace', 'dynasty', 'peninsula',
            'gyeongju', 'incheon', 'daegu', 'gwangju', 'daejeon', 'ulsan',
            'gangnam', 'hongdae', 'itaewon', 'myeongdong', 'dongdaemun',
            'chosun', 'chosen', 'corea', 'han river', 'hangang', 'namsan'
        ]

        # Secondary indicators (broader Asian context)
        asian_indicators = [
            'east asia', 'asia', 'asian', 'confucius', 'buddha', 'buddhism',
            'confucianism', 'pagoda', 'shrine', 'martial arts', 'calligraphy'
        ]

        # Cultural/architectural indicators
        cultural_indicators = [
            'traditional', 'heritage', 'cultural', 'historic', 'ancient',
            'royal', 'imperial', 'ceremonial', 'festival', 'ritual'
        ]

        text_to_check = (content + ' ' + title).lower()

        # Check primary Korean indicators (high confidence)
        for indicator in korean_indicators:
            if indicator in text_to_check:
                logger.debug(f"         ✅ Korean indicator found: '{indicator}'")
                return True

        # Check for Korean language characters (Hangul/Hanja patterns)
        korean_patterns = [
            r'korean:\s*[가-힣]+',  # Korean: 한글
            r'hanja:\s*[一-龯]+',   # Hanja: 漢字
            r'[가-힣]{2,}',         # Hangul characters
            r'romanized?:\s*[a-z\-]+',  # Romanized: something
        ]

        for pattern in korean_patterns:
            if re.search(pattern, text_to_check):
                logger.debug(f"         ✅ Korean language pattern found")
                return True

        # Check secondary indicators with cultural context
        asian_count = sum(1 for indicator in asian_indicators if indicator in text_to_check)
        cultural_count = sum(1 for indicator in cultural_indicators if indicator in text_to_check)

        if asian_count >= 1 and cultural_count >= 1:
            logger.debug(f"         ✅ Asian + cultural context found")
            return True

        # Special case: if title contains potential Korean romanization patterns
        if self._looks_like_korean_romanization(title):
            logger.debug(f"         ✅ Title looks like Korean romanization")
            return True

        logger.debug(f"         ❌ No Korean cultural indicators found")
        return False

    def _looks_like_korean_romanization(self, title: str) -> bool:
        """Check if title looks like Korean romanization."""
        # Common Korean romanization patterns
        korean_patterns = [
            r'[gk]y[eo]ng',  # gyeong, kyeong (경)
            r'[gk]w[ao]ng',  # gwang, kwang (광)
            r'[dt]ae',       # dae, tae (대)
            r'[jc]h?[eo]ng', # jeong, cheong (정)
            r'[bp]u[kg]',    # buk, puk (북)
            r'[dt]ong',      # dong, tong (동)
            r'[hs]an',       # han, san (한, 산)
            r'[gk]u[mn]',    # gun, kun, gum, kum (군, 금)
            r'[jc]u',        # ju, chu (주)
            r'[wy]ol',       # wol, yol (월)
            r'gyo$',         # gyo (교) - bridge
            r'gung$',        # gung (궁) - palace
            r'sa$',          # sa (사) - temple
        ]

        title_lower = title.lower()
        for pattern in korean_patterns:
            if re.search(pattern, title_lower):
                return True

        return False

    def test_connection(self) -> bool:
        """Test Wikipedia API connection."""
        try:
            # Try a simple search
            results = wikipedia.search("Korea", results=1)
            return len(results) > 0
        except Exception as e:
            logger.error(f"Wikipedia connection test failed: {str(e)}")
            return False
