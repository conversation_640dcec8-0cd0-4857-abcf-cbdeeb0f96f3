"""
Wikipedia Handler Module for VQA Dataset Enhancement System
Manages Wikipedia API interactions and content extraction.
"""

import wikipedia
import logging
import re
from typing import Optional, <PERSON><PERSON>
from config import WIKIPEDIA_LANGUAGE, MAX_SENTENCES, TIMEOUT_SECONDS

logger = logging.getLogger(__name__)

class WikipediaHandler:
    """Handles Wikipedia page retrieval and content extraction."""

    def __init__(self):
        wikipedia.set_lang(WIKIPEDIA_LANGUAGE)
        wikipedia.set_rate_limiting(True)

    def get_knowledge_point(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Retrieve knowledge point and source URL from Wikipedia.

        Args:
            keyword: The search term for Wikipedia

        Returns:
            Tuple of (knowledge_point, source_url) or (None, None) if not found
        """
        if not keyword:
            logger.warning(f"     ⚠️  Empty keyword provided to Wikipedia search")
            return None, None

        try:
            logger.info(f"     🔍 Searching Wikipedia for: '{keyword}'")

            # Search for the page
            logger.info(f"     📄 Step 1: Finding Wikipedia page...")
            page = self._get_wikipedia_page(keyword)
            if not page:
                logger.warning(f"     ❌ No Wikipedia page found for: '{keyword}'")
                return None, None

            logger.info(f"     ✅ Found page: '{page.title}'")

            # Extract content
            logger.info(f"     📝 Step 2: Extracting content from page...")
            knowledge_point = self._extract_knowledge_point(page)
            if not knowledge_point:
                logger.warning(f"     ❌ No valid content extracted from page: {page.title}")
                return None, None

            logger.info(f"     📏 Extracted {len(knowledge_point)} characters of content")

            # Validate Korean cultural relevance
            logger.info(f"     🏛️  Step 3: Validating Korean cultural relevance...")
            if not self._is_korean_cultural_content(knowledge_point, page.title):
                logger.warning(f"     ❌ Content not relevant to Korean culture: {page.title}")
                return None, None

            source_url = page.url
            logger.info(f"     🎉 Successfully retrieved knowledge from: {page.title}")
            logger.info(f"     🔗 Source URL: {source_url}")

            return knowledge_point, source_url

        except Exception as e:
            logger.error(f"     💥 Error retrieving Wikipedia content for '{keyword}': {str(e)}")
            return None, None

    def _get_wikipedia_page(self, keyword: str) -> Optional[wikipedia.WikipediaPage]:
        """Get Wikipedia page for the given keyword."""
        try:
            # First try exact match
            logger.debug(f"       🎯 Trying exact match for '{keyword}'...")
            try:
                page = wikipedia.page(keyword, auto_suggest=False)
                logger.info(f"       ✅ Exact match found: '{page.title}'")
                return page
            except wikipedia.DisambiguationError as e:
                # Handle disambiguation - try the first option
                if e.options:
                    logger.info(f"       🔀 Disambiguation found for '{keyword}', trying: {e.options[0]}")
                    try:
                        page = wikipedia.page(e.options[0], auto_suggest=False)
                        logger.info(f"       ✅ Disambiguation resolved: '{page.title}'")
                        return page
                    except:
                        logger.debug(f"       ❌ Disambiguation option failed")
                        pass
            except wikipedia.PageError:
                logger.debug(f"       ❌ Exact match failed - page not found")
                pass

            # Try with auto-suggest
            logger.debug(f"       🔍 Trying auto-suggest for '{keyword}'...")
            try:
                page = wikipedia.page(keyword, auto_suggest=True)
                logger.info(f"       ✅ Auto-suggest found: '{page.title}'")
                return page
            except wikipedia.DisambiguationError as e:
                if e.options:
                    logger.info(f"       🔀 Auto-suggest disambiguation for '{keyword}', trying: {e.options[0]}")
                    try:
                        page = wikipedia.page(e.options[0], auto_suggest=False)
                        logger.info(f"       ✅ Auto-suggest disambiguation resolved: '{page.title}'")
                        return page
                    except:
                        logger.debug(f"       ❌ Auto-suggest disambiguation failed")
                        pass
            except wikipedia.PageError:
                logger.debug(f"       ❌ Auto-suggest failed - page not found")
                pass

            # Try search and get first result
            logger.debug(f"       🔎 Trying search for '{keyword}'...")
            search_results = wikipedia.search(keyword, results=3)
            logger.info(f"       📋 Search returned {len(search_results)} results: {search_results}")

            for i, result in enumerate(search_results):
                try:
                    logger.debug(f"       🎯 Trying search result {i+1}: '{result}'")
                    page = wikipedia.page(result, auto_suggest=False)
                    logger.info(f"       ✅ Search result found: '{page.title}'")
                    return page
                except:
                    logger.debug(f"       ❌ Search result {i+1} failed")
                    continue

            logger.warning(f"       ❌ All Wikipedia search methods failed for '{keyword}'")
            return None

        except Exception as e:
            logger.error(f"       💥 Error getting Wikipedia page for '{keyword}': {str(e)}")
            return None

    def _extract_knowledge_point(self, page: wikipedia.WikipediaPage) -> Optional[str]:
        """Extract the first paragraph as knowledge point."""
        try:
            content = page.content
            if not content:
                return None

            # Get the first paragraph (before the first section)
            paragraphs = content.split('\n\n')
            first_paragraph = None

            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                # Skip empty paragraphs and section headers
                if paragraph and not paragraph.startswith('==') and len(paragraph) > 50:
                    first_paragraph = paragraph
                    break

            if not first_paragraph:
                return None

            # Clean the paragraph
            knowledge_point = self._clean_paragraph(first_paragraph)

            # Limit to MAX_SENTENCES if too long
            if knowledge_point:
                knowledge_point = self._limit_sentences(knowledge_point, MAX_SENTENCES)

            return knowledge_point

        except Exception as e:
            logger.error(f"Error extracting content from page: {str(e)}")
            return None

    def _clean_paragraph(self, paragraph: str) -> str:
        """Clean Wikipedia paragraph text."""
        # Remove Wikipedia markup
        paragraph = re.sub(r'\[\[([^\]]+)\]\]', r'\1', paragraph)  # Remove links
        paragraph = re.sub(r'\[([^\]]+)\]', '', paragraph)  # Remove references
        paragraph = re.sub(r'\{\{[^}]+\}\}', '', paragraph)  # Remove templates

        # Clean up whitespace
        paragraph = ' '.join(paragraph.split())

        return paragraph.strip()

    def _limit_sentences(self, text: str, max_sentences: int) -> str:
        """Limit text to maximum number of sentences."""
        # Split by sentence endings
        sentences = re.split(r'[.!?]+', text)

        # Keep only the first max_sentences
        if len(sentences) > max_sentences:
            # Take first max_sentences and add proper ending
            limited_sentences = sentences[:max_sentences]
            result = '. '.join(s.strip() for s in limited_sentences if s.strip())
            if result and not result.endswith('.'):
                result += '.'
            return result

        return text

    def _is_korean_cultural_content(self, content: str, title: str) -> bool:
        """Check if content is relevant to Korean culture."""
        korean_indicators = [
            'korea', 'korean', 'seoul', 'busan', 'jeju', 'joseon', 'goryeo', 'silla',
            'baekje', 'goguryeo', 'hanbok', 'kimchi', 'taekwondo', 'hangul',
            'confucian', 'buddhist', 'temple', 'palace', 'dynasty', 'peninsula'
        ]

        text_to_check = (content + ' ' + title).lower()

        # Check if any Korean indicators are present
        for indicator in korean_indicators:
            if indicator in text_to_check:
                return True

        # Additional check for East Asian context
        east_asian_indicators = ['east asia', 'asia', 'asian', 'confucius', 'buddha']
        for indicator in east_asian_indicators:
            if indicator in text_to_check:
                return True

        return False

    def test_connection(self) -> bool:
        """Test Wikipedia API connection."""
        try:
            # Try a simple search
            results = wikipedia.search("Korea", results=1)
            return len(results) > 0
        except Exception as e:
            logger.error(f"Wikipedia connection test failed: {str(e)}")
            return False
