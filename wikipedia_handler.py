"""
Enhanced Wikipedia Handler Module for VQA Dataset Enhancement System
Uses multiple search strategies to find Korean cultural information.
"""

import wikipedia
import requests
import logging
import re
import time
from typing import Optional, Tuple, List, Dict
from config import MAX_SENTENCES, TIMEOUT_SECONDS

logger = logging.getLogger(__name__)

class EnhancedWikipediaHandler:
    """Enhanced Wikipedia handler with multiple search strategies."""

    def __init__(self):
        wikipedia.set_lang('en')
        wikipedia.set_rate_limiting(True)

        # Common Korean terms and their English equivalents
        self.korean_terms = {
            '궁': 'Palace',
            '사': 'Temple',
            '산': 'Mountain',
            '강': 'River',
            '교': 'Bridge',
            '문': 'Gate',
            '탑': 'Tower',
            '공원': 'Park',
            '시장': 'Market',
            '대학교': 'University',
            '역': 'Station',
            '구': 'District',
            '동': 'District',
            '로': 'Road',
            '길': 'Street'
        }

    def get_knowledge_point(self, keyword: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Retrieve knowledge point and source URL using enhanced Wikipedia search.

        Args:
            keyword: The search term

        Returns:
            Tuple of (knowledge_point, source_url) or (None, None) if not found
        """
        if not keyword:
            logger.warning(f"     ⚠️  Empty keyword provided")
            return None, None

        try:
            logger.info(f"     🔍 Enhanced Wikipedia search for: '{keyword}'")

            # Generate search variations
            search_terms = self._generate_search_terms(keyword)
            logger.info(f"     📝 Search terms: {search_terms[:3]}...")  # Show first 3

            # Try each search term
            for i, search_term in enumerate(search_terms):
                logger.info(f"     🎯 Trying term {i+1}: '{search_term}'")

                try:
                    page = self._get_wikipedia_page(search_term)
                    if page:
                        # Extract and validate content
                        knowledge_point = self._extract_knowledge_point(page)
                        if knowledge_point and self._is_korean_cultural_content(knowledge_point, page.title):
                            logger.info(f"     ✅ Found relevant content: {page.title}")
                            logger.info(f"     📄 Content preview: {knowledge_point[:80]}...")
                            return knowledge_point, page.url
                        else:
                            logger.debug(f"     ⚠️  Content not suitable, trying next term...")
                            continue
                    else:
                        logger.debug(f"     ❌ No page found for: '{search_term}'")

                except Exception as e:
                    logger.debug(f"     ⚠️  Search failed for '{search_term}': {str(e)}")
                    continue

                # Small delay between searches
                time.sleep(0.5)

            logger.warning(f"     ❌ No relevant content found for: '{keyword}'")
            return None, None

        except Exception as e:
            logger.error(f"     💥 Error in enhanced search for '{keyword}': {str(e)}")
            return None, None

    def _generate_search_terms(self, keyword: str) -> List[str]:
        """Generate multiple search term variations."""
        terms = [keyword]

        # Add "Korea" or "Korean" to make searches more specific
        if 'korea' not in keyword.lower():
            terms.extend([
                f"{keyword} Korea",
                f"{keyword} Korean",
                f"Korean {keyword}"
            ])

        # Add common suffixes if not present
        suffixes = ['Palace', 'Temple', 'Mountain', 'Bridge', 'Tower', 'Gate', 'Park']
        for suffix in suffixes:
            if suffix.lower() not in keyword.lower():
                terms.append(f"{keyword} {suffix}")

        # Remove duplicates while preserving order
        seen = set()
        unique_terms = []
        for term in terms:
            if term not in seen:
                seen.add(term)
                unique_terms.append(term)

        return unique_terms[:8]  # Limit to 8 terms to avoid too many requests

    def _get_wikipedia_page(self, search_term: str) -> Optional[wikipedia.WikipediaPage]:
        """Get Wikipedia page using multiple strategies."""
        try:
            # Strategy 1: Direct page lookup
            try:
                page = wikipedia.page(search_term, auto_suggest=False)
                logger.debug(f"       ✅ Direct match: {page.title}")
                return page
            except wikipedia.DisambiguationError as e:
                # Handle disambiguation - try first option
                if e.options:
                    try:
                        page = wikipedia.page(e.options[0], auto_suggest=False)
                        logger.debug(f"       ✅ Disambiguation resolved: {page.title}")
                        return page
                    except:
                        pass
            except wikipedia.PageError:
                pass

            # Strategy 2: Auto-suggest
            try:
                page = wikipedia.page(search_term, auto_suggest=True)
                logger.debug(f"       ✅ Auto-suggest found: {page.title}")
                return page
            except:
                pass

            # Strategy 3: Search and try results
            try:
                search_results = wikipedia.search(search_term, results=3)
                for result in search_results:
                    try:
                        page = wikipedia.page(result, auto_suggest=False)
                        logger.debug(f"       ✅ Search result found: {page.title}")
                        return page
                    except:
                        continue
            except:
                pass

            return None

        except Exception as e:
            logger.debug(f"       ❌ Error getting page for '{search_term}': {str(e)}")
            return None

    def _extract_knowledge_point(self, page: wikipedia.WikipediaPage) -> Optional[str]:
        """Extract the first paragraph as knowledge point."""
        try:
            content = page.content
            if not content:
                return None

            # Get the first paragraph (before the first section)
            paragraphs = content.split('\n\n')
            first_paragraph = None

            for paragraph in paragraphs:
                paragraph = paragraph.strip()
                # Skip empty paragraphs and section headers
                if paragraph and not paragraph.startswith('==') and len(paragraph) > 50:
                    first_paragraph = paragraph
                    break

            if not first_paragraph:
                return None

            # Clean the paragraph
            knowledge_point = self._clean_paragraph(first_paragraph)

            # Limit to MAX_SENTENCES if too long
            if knowledge_point:
                knowledge_point = self._limit_sentences(knowledge_point, MAX_SENTENCES)

            return knowledge_point

        except Exception as e:
            logger.error(f"Error extracting content from page: {str(e)}")
            return None

    def _clean_paragraph(self, paragraph: str) -> str:
        """Clean Wikipedia paragraph text."""
        # Remove Wikipedia markup
        paragraph = re.sub(r'\[\[([^\]]+)\]\]', r'\1', paragraph)  # Remove links
        paragraph = re.sub(r'\[([^\]]+)\]', '', paragraph)  # Remove references
        paragraph = re.sub(r'\{\{[^}]+\}\}', '', paragraph)  # Remove templates

        # Clean up whitespace
        paragraph = ' '.join(paragraph.split())

        return paragraph.strip()

    def _limit_sentences(self, text: str, max_sentences: int) -> str:
        """Limit text to maximum number of sentences."""
        # Split by sentence endings
        sentences = re.split(r'[.!?]+', text)

        # Keep only the first max_sentences
        if len(sentences) > max_sentences:
            # Take first max_sentences and add proper ending
            limited_sentences = sentences[:max_sentences]
            result = '. '.join(s.strip() for s in limited_sentences if s.strip())
            if result and not result.endswith('.'):
                result += '.'
            return result

        return text

    def _is_korean_cultural_content(self, content: str, title: str) -> bool:
        """Check if content is relevant to Korean culture."""
        if not content:
            return False

        # Primary Korean indicators
        korean_indicators = [
            'korea', 'korean', 'seoul', 'busan', 'jeju', 'joseon', 'goryeo', 'silla',
            'baekje', 'goguryeo', 'hanbok', 'kimchi', 'taekwondo', 'hangul',
            'confucian', 'buddhist', 'temple', 'palace', 'dynasty', 'peninsula',
            'gyeongju', 'incheon', 'daegu', 'gwangju', 'daejeon', 'ulsan',
            'gangnam', 'hongdae', 'itaewon', 'myeongdong', 'dongdaemun',
            'chosun', 'chosen', 'corea', 'han river', 'hangang', 'namsan'
        ]

        # Cultural/architectural indicators
        cultural_indicators = [
            'traditional', 'heritage', 'cultural', 'historic', 'ancient',
            'royal', 'imperial', 'ceremonial', 'festival', 'ritual',
            'architecture', 'building', 'structure', 'monument'
        ]

        text_to_check = (content + ' ' + title).lower()

        # Check primary Korean indicators
        korean_count = sum(1 for indicator in korean_indicators if indicator in text_to_check)
        cultural_count = sum(1 for indicator in cultural_indicators if indicator in text_to_check)

        # Must have at least one Korean indicator
        if korean_count >= 1:
            logger.debug(f"       ✅ Korean cultural content detected")
            return True

        # Or have cultural indicators with Asian context
        if cultural_count >= 2 and any(term in text_to_check for term in ['asia', 'asian', 'east']):
            logger.debug(f"       ✅ Asian cultural content detected")
            return True

        logger.debug(f"       ❌ Not Korean cultural content")
        return False

    def test_connection(self) -> bool:
        """Test if Wikipedia search is working."""
        try:
            # Test with a simple search
            results = wikipedia.search("Korea", results=1)
            return len(results) > 0
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False



# For backward compatibility, create aliases
WikipediaHandler = EnhancedWikipediaHandler
WebKnowledgeHandler = EnhancedWikipediaHandler

