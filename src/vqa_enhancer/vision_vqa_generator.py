"""
Vision-based VQA Generator
Generates Korean cultural questions using OpenAI's GPT-4o-mini vision model.
"""

import os
import logging
import pandas as pd
import requests
from typing import Dict, Any, Optional, List, Tuple
from PIL import Image
import base64
import io
import time
from openai import OpenAI
from dotenv import load_dotenv

logger = logging.getLogger(__name__)


class VisionVQAGenerator:
    """Generates VQA questions using OpenAI's vision model."""

    def __init__(self):
        """Initialize the vision VQA generator."""
        load_dotenv()

        self.client = None
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-4o-mini"  # Using gpt-4o-mini for vision support
        self.max_retries = 3
        self.retry_delay = 2

        # Statistics tracking
        self.stats = {
            "total_rows": 0,
            "processed_rows": 0,
            "skipped_invalid_urls": 0,
            "skipped_image_errors": 0,
            "api_errors": 0,
            "successful_generations": 0,
        }

    def initialize(self) -> bool:
        """Initialize the OpenAI client."""
        try:
            if not self.api_key:
                logger.error("❌ OPENAI_API_KEY not found in environment variables")
                logger.info("💡 Please set OPENAI_API_KEY in your .env file")
                return False

            self.client = OpenAI(api_key=self.api_key)

            # Test the connection
            logger.info("🔍 Testing OpenAI API connection...")
            test_response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
            )

            logger.info("✅ OpenAI API connection successful")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {str(e)}")
            return False

    def _load_image_from_url(self, url: str) -> Optional[str]:
        """Load image from URL and convert to base64."""
        try:
            logger.debug(f"📥 Loading image from: {url[:80]}...")

            # Download image
            response = requests.get(url, timeout=30, stream=True)
            response.raise_for_status()

            # Check content type
            content_type = response.headers.get("content-type", "")
            if not content_type.startswith("image/"):
                logger.warning(f"⚠️  URL does not point to an image: {content_type}")
                return None

            # Load and validate image
            image_data = response.content
            image = Image.open(io.BytesIO(image_data))

            # Convert to RGB if necessary
            if image.mode != "RGB":
                image = image.convert("RGB")

            # Resize if too large (max 2048x2048 for OpenAI)
            max_size = 2048
            if image.width > max_size or image.height > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

            # Convert to base64
            buffer = io.BytesIO()
            image.save(buffer, format="JPEG", quality=85)
            image_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")

            logger.debug(f"✅ Image loaded successfully ({len(image_base64)} bytes)")
            return image_base64

        except requests.exceptions.RequestException as e:
            logger.warning(f"🌐 Network error loading image: {str(e)}")
            return None
        except Exception as e:
            logger.warning(f"🖼️  Error processing image: {str(e)}")
            return None

    def _generate_question_with_vision(
        self, image_base64: str, keyword: str, category: str
    ) -> Optional[Dict[str, Any]]:
        """Generate VQA question using OpenAI vision model."""
        try:
            # Create the prompt for question generation
            prompt = f"""You are an expert in Korean culture creating VQA (Visual Question Answering) questions.

CONTEXT:
- Category: {category}
- Cultural Keyword/Concept: {keyword}
- You must analyze the provided image and create a question about Korean culture

REQUIREMENTS:
1. **Multi-hop reasoning**: Question must combine at least 2 facts/concepts beyond visual recognition
2. **Image-dependent**: Question must be unsolvable without seeing the image
3. **Korean cultural focus**: Must relate to Korean culture, history, or society
4. **Object references**: Use "this building," "this dish," "this location," etc. (DO NOT mention the keyword directly)
5. **Question starters**: Begin with "Choose the...", "Which...?", "What type of...?"
6. **Diverse reasoning**: Use causal, temporal, spatial, comparative, or functional reasoning

TASK:
Generate exactly 4 answer options:
- 1 correct answer
- 2 plausible but incorrect answers (consistent with image)
- 1 clearly inconsistent answer (obviously wrong from image)

OUTPUT FORMAT (JSON):
{{
    "question": "Your question here",
    "option_1": "First option",
    "option_2": "Second option",
    "option_3": "Third option",
    "option_4": "Fourth option",
    "correct_option": 1-4 (index of correct answer)
}}

EXAMPLES:
- "Which historical period's architectural style is primarily demonstrated by this building's roof design and courtyard layout?"
- "What type of social gathering tradition is this scene most likely representing based on the setting and participant arrangement?"
- "Choose the cultural significance that this food preparation method historically served in Korean society."

Generate the VQA question now:"""

            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}",
                                    "detail": "high",
                                },
                            },
                        ],
                    }
                ],
                max_tokens=500,
                temperature=0.7,
            )

            # Parse response
            content = response.choices[0].message.content.strip()
            logger.debug(f"🔍 Raw API response: {content}")

            # Extract JSON from response
            import json
            import re

            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    result = json.loads(json_str)

                    # Validate required fields
                    required_fields = [
                        "question",
                        "option_1",
                        "option_2",
                        "option_3",
                        "option_4",
                        "correct_option",
                    ]
                    if all(field in result for field in required_fields):
                        # Validate correct_option is 1-4
                        if 1 <= result["correct_option"] <= 4:
                            logger.debug("✅ Successfully generated VQA question")
                            return result
                        else:
                            logger.warning("⚠️  Invalid correct_option value")
                    else:
                        logger.warning("⚠️  Missing required fields in response")
                except json.JSONDecodeError:
                    logger.warning("⚠️  Failed to parse JSON response")

            logger.warning("⚠️  Could not extract valid JSON from API response")
            return None

        except Exception as e:
            logger.error(f"💥 Error generating question with vision: {str(e)}")
            return None

    def _process_row(self, row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """Process a single row to generate VQA question."""
        try:
            # Extract data from row
            image_url = row.get("Image Source", "")
            keyword = row.get("Keyword/Concept", "")
            category = row.get("Main Category", "")

            # Skip if no image URL
            if not image_url or pd.isna(image_url) or image_url.strip() == "":
                logger.info(f"   ⏭️  Row {row_idx + 1}: No image URL, skipping")
                self.stats["skipped_invalid_urls"] += 1
                return None

            # Skip if no keyword
            if not keyword or pd.isna(keyword) or keyword.strip() == "":
                logger.info(f"   ⏭️  Row {row_idx + 1}: No keyword, skipping")
                self.stats["skipped_invalid_urls"] += 1
                return None

            logger.info(f"   🖼️  Processing: {keyword}")

            # Load image
            image_base64 = self._load_image_from_url(image_url.strip())
            if not image_base64:
                logger.info(f"   ❌ Failed to load image for: {keyword}")
                self.stats["skipped_image_errors"] += 1
                return None

            # Generate question with retries
            for attempt in range(self.max_retries):
                try:
                    result = self._generate_question_with_vision(
                        image_base64=image_base64,
                        keyword=keyword.strip(),
                        category=category.strip() if category else "General",
                    )

                    if result:
                        logger.info(f"   ✅ Generated question for: {keyword}")
                        self.stats["successful_generations"] += 1
                        return result
                    else:
                        logger.warning(
                            f"   ⚠️  Attempt {attempt + 1} failed for: {keyword}"
                        )

                except Exception as e:
                    logger.warning(f"   💥 Attempt {attempt + 1} error: {str(e)}")

                # Wait before retry
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

            logger.error(f"   ❌ All attempts failed for: {keyword}")
            self.stats["api_errors"] += 1
            return None

        except Exception as e:
            logger.error(f"   💥 Error processing row {row_idx + 1}: {str(e)}")
            return None

    def generate_vqa_dataset(
        self, input_file: str, output_file: str = None, max_rows: int = None
    ) -> bool:
        """Generate VQA questions for the entire dataset."""
        try:
            logger.info("🚀 Starting Vision VQA Generation")
            logger.info("=" * 60)

            # Load dataset
            logger.info(f"📂 Loading dataset: {input_file}")
            df = pd.read_csv(input_file)

            # Determine output file
            if output_file is None:
                from pathlib import Path

                input_path = Path(input_file)
                output_file = str(
                    input_path.parent
                    / f"{input_path.stem}_vision_vqa{input_path.suffix}"
                )

            # Create backup
            backup_file = output_file.replace(".csv", "_backup.csv")
            df.to_csv(backup_file, index=False)
            logger.info(f"💾 Backup created: {backup_file}")

            # Determine rows to process
            total_rows = len(df)
            if max_rows:
                total_rows = min(max_rows, total_rows)
                df = df.head(max_rows)
                logger.info(f"📊 Processing first {total_rows} rows")
            else:
                logger.info(f"📊 Processing all {total_rows} rows")

            self.stats["total_rows"] = total_rows

            # Process each row
            logger.info(f"🔄 Starting VQA generation...")

            for idx, row in df.iterrows():
                if idx >= total_rows:
                    break

                progress = ((idx + 1) / total_rows) * 100
                logger.info(
                    f"🔄 Processing row {idx + 1}/{total_rows} ({progress:.1f}% complete)"
                )

                # Generate VQA for this row
                result = self._process_row(row, idx)

                if result:
                    # Update the dataframe with generated content
                    df.at[idx, "Question"] = result["question"]
                    df.at[idx, "Option 1"] = result["option_1"]
                    df.at[idx, "Option 2"] = result["option_2"]
                    df.at[idx, "Option 3"] = result["option_3"]
                    df.at[idx, "Option 4"] = result["option_4"]
                    df.at[idx, "Correct Option"] = result["correct_option"]

                    self.stats["processed_rows"] += 1
                    logger.info(f"   ✅ Row {idx + 1} updated successfully")
                else:
                    logger.info(f"   ⏭️  Row {idx + 1} skipped")

                # Save progress periodically (every 10 rows)
                if (idx + 1) % 10 == 0:
                    df.to_csv(output_file, index=False)
                    logger.info(f"💾 Progress saved ({idx + 1} rows processed)")

            # Final save
            df.to_csv(output_file, index=False)
            logger.info(f"💾 Final dataset saved: {output_file}")

            # Print statistics
            self._print_statistics()

            return True

        except Exception as e:
            logger.error(f"💥 Error generating VQA dataset: {str(e)}")
            return False

    def _print_statistics(self):
        """Print generation statistics."""
        logger.info("=" * 60)
        logger.info("📊 Vision VQA Generation Statistics")
        logger.info("=" * 60)
        logger.info(f"Total rows: {self.stats['total_rows']}")
        logger.info(f"Successfully processed: {self.stats['processed_rows']}")
        logger.info(f"Successful generations: {self.stats['successful_generations']}")
        logger.info(f"Skipped (invalid URLs): {self.stats['skipped_invalid_urls']}")
        logger.info(f"Skipped (image errors): {self.stats['skipped_image_errors']}")
        logger.info(f"API errors: {self.stats['api_errors']}")

        if self.stats["total_rows"] > 0:
            success_rate = (
                self.stats["successful_generations"] / self.stats["total_rows"]
            ) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")

        logger.info("=" * 60)

    def test_single_generation(
        self, image_url: str, keyword: str, category: str = "Test"
    ) -> bool:
        """Test generation with a single image."""
        try:
            logger.info("🧪 Testing single VQA generation")
            logger.info(f"🖼️  Image URL: {image_url[:80]}...")
            logger.info(f"🏷️  Keyword: {keyword}")
            logger.info(f"📂 Category: {category}")

            # Load image
            image_base64 = self._load_image_from_url(image_url)
            if not image_base64:
                logger.error("❌ Failed to load test image")
                return False

            # Generate question
            result = self._generate_question_with_vision(
                image_base64, keyword, category
            )

            if result:
                logger.info("✅ Test generation successful!")
                logger.info(f"❓ Question: {result['question']}")
                logger.info(f"1️⃣  Option 1: {result['option_1']}")
                logger.info(f"2️⃣  Option 2: {result['option_2']}")
                logger.info(f"3️⃣  Option 3: {result['option_3']}")
                logger.info(f"4️⃣  Option 4: {result['option_4']}")
                logger.info(f"✅ Correct: {result['correct_option']}")
                return True
            else:
                logger.error("❌ Test generation failed")
                return False

        except Exception as e:
            logger.error(f"💥 Test generation error: {str(e)}")
            return False
