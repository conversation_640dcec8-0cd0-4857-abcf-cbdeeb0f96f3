"""
Keyword Wikipedia Enhancer

This module reads a complete VQA CSV file and generates relevant keywords for Wikipedia search
to populate knowledge points. It searches both English and Korean Wikipedia APIs.
"""

import os
import logging
import pandas as pd
import time
import requests
from typing import Dict, Any, Optional, List, Tuple
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class KeywordWikipediaEnhancer:
    """Enhance VQA dataset with Wikipedia knowledge points using generated keywords."""

    def __init__(self):
        """Initialize the Keyword Wikipedia Enhancer."""
        self.client = None
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-4o-mini"
        self.max_keyword_attempts = 5
        self.retry_delay = 1

        # Statistics tracking
        self.stats = {
            "total_rows": 0,
            "processed_rows": 0,
            "successful_wikipedia": 0,
            "failed_wikipedia": 0,
            "skipped_rows": 0,
            "english_wiki_success": 0,
            "korean_wiki_success": 0,
        }

    def initialize(self) -> bool:
        """Initialize the OpenAI client."""
        try:
            if not self.api_key:
                logger.error("❌ OPENAI_API_KEY not found in environment variables")
                logger.info("💡 Please set OPENAI_API_KEY in your .env file")
                return False

            self.client = OpenAI(api_key=self.api_key)

            # Test the connection
            logger.info("🔍 Testing OpenAI API connection...")
            test_response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
            )

            logger.info("✅ OpenAI API connection successful")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {str(e)}")
            return False

    def _generate_keyword(
        self,
        question: str,
        options: List[str],
        category: str,
        previous_keywords: List[str],
    ) -> Optional[str]:
        """Generate a relevant keyword for Wikipedia search."""
        try:
            # Create prompt for keyword generation
            options_text = "\n".join(
                [f"- {opt}" for opt in options if opt and opt.strip()]
            )
            previous_text = (
                ", ".join(previous_keywords) if previous_keywords else "None"
            )

            prompt = f"""You are an expert in Korean culture and Wikipedia search optimization.

TASK: Generate a specific, searchable keyword for Wikipedia that is relevant to this Korean cultural VQA question.

CONTEXT:
- Category: {category}
- Question: {question}
- Answer Options:
{options_text}

REQUIREMENTS:
1. **Wikipedia searchable**: Must be a term that likely exists on Wikipedia
2. **Relevant**: Must relate to the question content or answer options
3. **Specific**: Prefer specific terms over general concepts
4. **Category appropriate**: Must fit within the {category} category
5. **Unique**: Must be different from previous attempts: {previous_text}

EXAMPLES OF GOOD KEYWORDS:
- For questions about Korean palaces: "Gyeongbokgung Palace", "Joseon Dynasty", "Korean royal architecture"
- For questions about K-pop: "BTS", "Korean Wave", "K-pop industry"
- For questions about food: "Kimchi", "Korean cuisine", "Bulgogi"
- For questions about education: "Korean education system", "Suneung", "Hagwon"

OUTPUT: Return only the keyword phrase (2-4 words maximum), nothing else.

Generate the keyword now:"""

            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=50,
                temperature=0.7,
            )

            # Parse response
            keyword = response.choices[0].message.content.strip()

            # Clean up the keyword (remove quotes, extra text)
            keyword = keyword.replace('"', "").replace("'", "").strip()

            # Take only the first line if multiple lines
            keyword = keyword.split("\n")[0].strip()

            logger.info(f"🔍 Generated keyword: {keyword}")
            return keyword

        except Exception as e:
            logger.error(f"💥 Error generating keyword: {str(e)}")
            return None

    def _search_wikipedia(
        self, keyword: str, language: str = "en"
    ) -> Optional[Tuple[str, str]]:
        """Search Wikipedia for a keyword and return first paragraph + URL."""
        try:
            # Set up Wikipedia API endpoint
            if language == "en":
                api_url = "https://en.wikipedia.org/w/api.php"
                wiki_url_base = "https://en.wikipedia.org/wiki"
            else:  # Korean
                api_url = "https://ko.wikipedia.org/w/api.php"
                wiki_url_base = "https://ko.wikipedia.org/wiki"

            headers = {
                "User-Agent": "VQA-Enhancer/1.0 (https://github.com/vqa-enhancer)"
            }

            # First, search for pages matching the keyword
            search_params = {
                "action": "query",
                "format": "json",
                "list": "search",
                "srsearch": keyword,
                "srlimit": 3,
                "srprop": "snippet",
            }

            search_response = requests.get(
                api_url, params=search_params, headers=headers, timeout=10
            )

            if search_response.status_code == 200:
                search_data = search_response.json()
                search_results = search_data.get("query", {}).get("search", [])

                if search_results:
                    # Try the first few search results
                    for result in search_results[:2]:  # Try top 2 results
                        page_title = result.get("title", "")

                        if page_title:
                            # Get the page content
                            content_params = {
                                "action": "query",
                                "format": "json",
                                "titles": page_title,
                                "prop": "extracts",
                                "exintro": True,
                                "explaintext": True,
                                "exsectionformat": "plain",
                            }

                            content_response = requests.get(
                                api_url,
                                params=content_params,
                                headers=headers,
                                timeout=10,
                            )

                            if content_response.status_code == 200:
                                content_data = content_response.json()
                                pages = content_data.get("query", {}).get("pages", {})

                                for page_id, page_info in pages.items():
                                    if page_id != "-1":  # Page exists
                                        extract = page_info.get("extract", "")
                                        title = page_info.get("title", page_title)

                                        if (
                                            extract and len(extract.strip()) > 100
                                        ):  # Ensure meaningful content
                                            # Create Wikipedia URL
                                            wiki_url = f"{wiki_url_base}/{title.replace(' ', '_')}"

                                            logger.info(
                                                f"✅ Found Wikipedia article: {title}"
                                            )
                                            return extract, wiki_url

            logger.info(f"❌ No suitable Wikipedia article found for: {keyword}")
            return None

        except Exception as e:
            logger.info(f"💥 Wikipedia search error for '{keyword}': {str(e)}")
            return None

    def _process_row(self, row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """Process a single row to generate keyword and search Wikipedia."""
        try:
            # Extract data from row
            question = row.get("Question", "")
            category = row.get("Main Category", "")

            # Skip if no question
            if not question or pd.isna(question) or question.strip() == "":
                logger.info(f"   ⏭️  Row {row_idx + 1}: No question, skipping")
                self.stats["skipped_rows"] += 1
                return None

            # Extract options
            options = []
            for i in range(1, 5):
                option = row.get(f"Option {i}", "")
                if option and not pd.isna(option) and option.strip():
                    options.append(option.strip())

            if not options:
                logger.info(f"   ⏭️  Row {row_idx + 1}: No options, skipping")
                self.stats["skipped_rows"] += 1
                return None

            logger.info(f"   🎯 Processing question: {question[:60]}...")

            # Try to generate keywords and search Wikipedia
            previous_keywords = []

            # Try English Wikipedia first (5 attempts)
            for attempt in range(self.max_keyword_attempts):
                keyword = self._generate_keyword(
                    question, options, category, previous_keywords
                )

                if keyword:
                    previous_keywords.append(keyword)
                    logger.info(
                        f"   🔍 Attempt {attempt + 1}/5 (EN): Trying keyword '{keyword}'"
                    )

                    result = self._search_wikipedia(keyword, "en")
                    if result:
                        extract, wiki_url = result
                        logger.info(f"   ✅ Found English Wikipedia: {keyword}")
                        self.stats["english_wiki_success"] += 1
                        return {
                            "knowledge_point": extract,
                            "knowledge_point_source": wiki_url,
                            "keyword_used": keyword,
                        }

                time.sleep(self.retry_delay)

            # Try Korean Wikipedia (5 attempts)
            logger.debug(f"   🔄 Trying Korean Wikipedia...")
            for attempt in range(self.max_keyword_attempts):
                keyword = self._generate_keyword(
                    question, options, category, previous_keywords
                )

                if keyword:
                    previous_keywords.append(keyword)
                    logger.info(
                        f"   🔍 Attempt {attempt + 1}/5 (KO): Trying keyword '{keyword}'"
                    )

                    result = self._search_wikipedia(keyword, "ko")
                    if result:
                        extract, wiki_url = result
                        logger.info(f"   ✅ Found Korean Wikipedia: {keyword}")
                        self.stats["korean_wiki_success"] += 1
                        return {
                            "knowledge_point": extract,
                            "knowledge_point_source": wiki_url,
                            "keyword_used": keyword,
                        }

                time.sleep(self.retry_delay)

            logger.info(f"   ❌ All 10 attempts failed for row {row_idx + 1}")
            self.stats["failed_wikipedia"] += 1
            return None

        except Exception as e:
            logger.error(f"   💥 Error processing row {row_idx + 1}: {str(e)}")
            return None

    def enhance_dataset(
        self, input_file: str, output_file: str = None, max_rows: int = None
    ) -> bool:
        """Enhance VQA dataset with Wikipedia knowledge points."""
        try:
            logger.info("🚀 Starting Keyword Wikipedia Enhancement")
            logger.info("=" * 60)

            # Load dataset
            logger.info(f"📂 Loading dataset: {input_file}")
            df = pd.read_csv(input_file)

            # Determine output file (overwrite input file to update it)
            if output_file is None:
                output_file = input_file
                logger.info(f"📝 Will update existing file: {output_file}")

            # Create backup
            backup_file = output_file.replace(".csv", "_backup.csv")
            df.to_csv(backup_file, index=False)
            logger.info(f"💾 Backup created: {backup_file}")

            # Determine rows to process
            total_rows = len(df)
            if max_rows:
                total_rows = min(max_rows, total_rows)
                df = df.head(max_rows)
                logger.info(f"📊 Processing first {total_rows} rows")
            else:
                logger.info(f"📊 Processing all {total_rows} rows")

            self.stats["total_rows"] = total_rows

            # Process each row
            logger.info(f"🔄 Starting keyword Wikipedia enhancement...")
            processed_count = 0

            for idx, row in df.iterrows():
                if max_rows and idx >= max_rows:
                    break

                progress = ((idx + 1) / total_rows) * 100
                logger.info(
                    f"🔄 Processing row {idx + 1}/{total_rows} ({progress:.1f}% complete)"
                )

                # Process this row
                result = self._process_row(row, idx)

                if result:
                    # Update the dataframe with Wikipedia content
                    df.at[idx, "Knowledge Point"] = result["knowledge_point"]
                    df.at[idx, "Knowledge Point Source"] = result[
                        "knowledge_point_source"
                    ]

                    self.stats["processed_rows"] += 1
                    self.stats["successful_wikipedia"] += 1
                    processed_count += 1
                    logger.info(f"   ✅ Row {idx + 1} updated with Wikipedia knowledge")
                else:
                    logger.info(f"   ⏭️  Row {idx + 1} skipped")

                # Save progress every 10 rows
                if (idx + 1) % 10 == 0:
                    df.to_csv(output_file, index=False)
                    logger.info(f"💾 Progress saved ({processed_count} rows updated)")

            # Final save
            df.to_csv(output_file, index=False)
            logger.info(f"💾 Final dataset saved: {output_file}")
            logger.info(f"🎯 Updated {processed_count} rows with Wikipedia knowledge")

            # Print statistics
            self._print_statistics()

            return True

        except Exception as e:
            logger.error(f"💥 Error enhancing dataset: {str(e)}")
            return False

    def _print_statistics(self):
        """Print enhancement statistics."""
        logger.info("=" * 60)
        logger.info("📊 Keyword Wikipedia Enhancement Statistics")
        logger.info("=" * 60)
        logger.info(f"Total rows: {self.stats['total_rows']}")
        logger.info(f"Successfully processed: {self.stats['processed_rows']}")
        logger.info(
            f"Successful Wikipedia searches: {self.stats['successful_wikipedia']}"
        )
        logger.info(f"Failed Wikipedia searches: {self.stats['failed_wikipedia']}")
        logger.info(f"Skipped rows: {self.stats['skipped_rows']}")
        logger.info(f"English Wikipedia success: {self.stats['english_wiki_success']}")
        logger.info(f"Korean Wikipedia success: {self.stats['korean_wiki_success']}")

        if self.stats["total_rows"] > 0:
            success_rate = (
                self.stats["successful_wikipedia"] / self.stats["total_rows"]
            ) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")

        logger.info("=" * 60)

    def test_single_enhancement(
        self, question: str, options: List[str], category: str = "Test"
    ) -> bool:
        """Test keyword generation and Wikipedia search with a single question."""
        try:
            logger.info("🧪 Testing single keyword Wikipedia enhancement")
            logger.info(f"❓ Question: {question}")
            logger.info(f"📂 Category: {category}")
            logger.info(f"📝 Options: {options}")

            # Create a mock row
            row_data = {
                "Question": question,
                "Main Category": category,
                "Option 1": options[0] if len(options) > 0 else "",
                "Option 2": options[1] if len(options) > 1 else "",
                "Option 3": options[2] if len(options) > 2 else "",
                "Option 4": options[3] if len(options) > 3 else "",
            }

            row = pd.Series(row_data)
            result = self._process_row(row, 0)

            if result:
                logger.info("✅ Test enhancement successful!")
                logger.info(f"🔑 Keyword used: {result['keyword_used']}")
                logger.info(f"📖 Knowledge: {result['knowledge_point'][:200]}...")
                logger.info(f"🔗 Source: {result['knowledge_point_source']}")
                return True
            else:
                logger.error("❌ Test enhancement failed")
                return False

        except Exception as e:
            logger.error(f"💥 Test enhancement error: {str(e)}")
            return False
