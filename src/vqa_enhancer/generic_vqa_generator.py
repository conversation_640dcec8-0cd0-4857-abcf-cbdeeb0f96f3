"""
Generic Korean Cultural VQA Generator

This module generates Korean cultural VQA questions for keywords without specific image URLs.
Questions focus on the cultural concept itself rather than specific visual details.
"""

import os
import logging
import pandas as pd
import json
import time
from typing import Dict, Any, Optional
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)


class GenericVQAGenerator:
    """Generate Korean cultural VQA questions for generic keyword images."""

    def __init__(self):
        """Initialize the Generic VQA Generator."""
        self.client = None
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.model = "gpt-4o-mini"
        self.max_retries = 3
        self.retry_delay = 2

        # Statistics tracking
        self.stats = {
            "total_rows": 0,
            "processed_rows": 0,
            "successful_generations": 0,
            "skipped_invalid_keywords": 0,
            "api_errors": 0,
        }

    def initialize(self) -> bool:
        """Initialize the OpenAI client."""
        try:
            if not self.api_key:
                logger.error("❌ OPENAI_API_KEY not found in environment variables")
                logger.info("💡 Please set OPENAI_API_KEY in your .env file")
                return False

            self.client = OpenAI(api_key=self.api_key)

            # Test the connection
            logger.info("🔍 Testing OpenAI API connection...")
            test_response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
            )

            logger.info("✅ OpenAI API connection successful")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {str(e)}")
            return False

    def _generate_generic_question(
        self, keyword: str, category: str
    ) -> Optional[Dict[str, Any]]:
        """Generate VQA question for a generic keyword without specific image details."""
        try:
            # Create the prompt for generic question generation
            prompt = f"""You are an expert in Korean culture creating VQA (Visual Question Answering) questions for VLLM benchmarking.

CONTEXT:
- Category: {category}
- Korean Cultural Keyword: {keyword}
- Task: Create a question about the CONCEPT itself, not specific visual details

CRITICAL REQUIREMENTS:
1. **Multi-hop reasoning**: Question must combine Korean cultural knowledge + historical/social context + conceptual understanding
2. **Generic image assumption**: Assume a generic image of the keyword (e.g., if keyword is "school", assume a generic school image)
3. **Korean cultural focus**: Test specific Korean cultural knowledge about this concept
4. **Object references**: Use "this building," "this food," "this practice," etc. (DO NOT mention the keyword directly)
5. **Question starters**: Begin with "Choose the...", "Which...?", "Considering..."
6. **NO SPECIFIC VISUAL DETAILS**: Cannot ask about colors, specific architectural details, people's clothing, etc.

WHAT YOU CAN ASK:
- Cultural significance of the concept
- Historical context and evolution
- Social role and function
- Traditional vs modern aspects
- Regional variations
- Cultural practices associated with it

WHAT YOU CANNOT ASK:
- Specific visual details (colors, materials, decorations)
- Specific people or individuals in the image
- Exact architectural features or design elements
- Specific text or signs visible

MULTI-HOP REASONING TYPES:
- Cultural concept + Korean historical context + Social significance
- Traditional practice + Korean regional variation + Modern adaptation
- Cultural function + Korean historical period + Contemporary relevance

TASK:
Generate exactly 4 answer options:
- 1 correct answer (requires specific Korean cultural knowledge)
- 2 plausible but incorrect answers (test Korean cultural knowledge)
- 1 clearly incorrect answer (obviously wrong about the concept)

Each option must be ONE SENTENCE maximum.

OUTPUT FORMAT (JSON):
{{
    "question": "Your question here",
    "option_1": "First option",
    "option_2": "Second option",
    "option_3": "Third option",
    "option_4": "Fourth option",
    "correct_option": 1-4 (index of correct answer)
}}

EXCELLENT EXAMPLES:
- "Considering the role of this institution in Korean society, which aspect best reflects its historical significance in Confucian education?"
- "Which traditional Korean practice is most commonly associated with this type of building during important cultural ceremonies?"
- "Choose the historical period when this architectural style became most prominent in Korean urban development."

AVOID:
- "What color is this building?" (too specific)
- "How many floors does this have?" (visual detail)
- "What is written on the sign?" (specific visual element)

Generate the Korean cultural VQA question now:"""

            # Make API call
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7,
            )

            # Parse response
            content = response.choices[0].message.content.strip()
            logger.debug(f"🔍 Raw API response: {content}")

            # Extract JSON from response
            import re

            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                try:
                    result = json.loads(json_str)

                    # Validate required fields
                    required_fields = [
                        "question",
                        "option_1",
                        "option_2",
                        "option_3",
                        "option_4",
                        "correct_option",
                    ]
                    if all(field in result for field in required_fields):
                        # Validate correct_option is 1-4
                        if 1 <= result["correct_option"] <= 4:
                            logger.debug(
                                "✅ Successfully generated generic VQA question"
                            )
                            return result
                        else:
                            logger.warning("⚠️  Invalid correct_option value")
                    else:
                        logger.warning("⚠️  Missing required fields in response")
                except json.JSONDecodeError:
                    logger.warning("⚠️  Failed to parse JSON response")

            logger.warning("⚠️  Could not extract valid JSON from API response")
            return None

        except Exception as e:
            logger.error(f"💥 Error generating generic question: {str(e)}")
            return None

    def _process_row(self, row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """Process a single row to generate generic VQA question."""
        try:
            # Extract data from row
            keyword = row.get("Keyword/Concept", "")
            category = row.get("Main Category", "")

            # Skip if no keyword
            if not keyword or pd.isna(keyword) or keyword.strip() == "":
                logger.info(f"   ⏭️  Row {row_idx + 1}: No keyword, skipping")
                self.stats["skipped_invalid_keywords"] += 1
                return None

            # Check if this row already has generated content (skip if already processed)
            question = row.get("Question", "")
            if question and not pd.isna(question) and question.strip() != "":
                logger.info(f"   ⏭️  Row {row_idx + 1}: Already has question, skipping")
                self.stats["skipped_invalid_keywords"] += 1
                return None

            logger.info(f"   🎯 Processing: {keyword}")

            # Generate question with retries
            for attempt in range(self.max_retries):
                try:
                    result = self._generate_generic_question(
                        keyword=keyword.strip(),
                        category=category.strip() if category else "General",
                    )

                    if result:
                        logger.info(f"   ✅ Generated question for: {keyword}")
                        self.stats["successful_generations"] += 1
                        return result
                    else:
                        logger.warning(
                            f"   ⚠️  Attempt {attempt + 1} failed for: {keyword}"
                        )

                except Exception as e:
                    logger.warning(f"   💥 Attempt {attempt + 1} error: {str(e)}")

                # Wait before retry
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)

            logger.error(f"   ❌ All attempts failed for: {keyword}")
            self.stats["api_errors"] += 1
            return None

        except Exception as e:
            logger.error(f"   💥 Error processing row {row_idx + 1}: {str(e)}")
            return None

    def generate_generic_vqa_dataset(
        self, input_file: str, output_file: str = None, max_rows: int = None
    ) -> bool:
        """Generate generic VQA questions for rows with empty vision-generated content."""
        try:
            logger.info("🚀 Starting Generic VQA Generation (Fallback Mode)")
            logger.info("=" * 60)

            # Load dataset
            logger.info(f"📂 Loading dataset: {input_file}")
            df = pd.read_csv(input_file)

            # Determine output file (overwrite input file to update it)
            if output_file is None:
                output_file = input_file
                logger.info(f"📝 Will update existing file: {output_file}")

            # Create backup
            backup_file = output_file.replace(".csv", "_backup.csv")
            df.to_csv(backup_file, index=False)
            logger.info(f"💾 Backup created: {backup_file}")

            # Find rows that need generic VQA generation (empty Question field)
            empty_question_mask = (
                df["Question"].isna()
                | (df["Question"] == "")
                | (df["Question"].str.strip() == "")
            )
            rows_needing_generation = df[empty_question_mask]

            total_rows = len(df)
            rows_to_process = len(rows_needing_generation)

            logger.info(f"📊 Total rows in dataset: {total_rows}")
            logger.info(f"📊 Rows needing generic VQA: {rows_to_process}")
            logger.info(f"📊 Rows already processed: {total_rows - rows_to_process}")

            if rows_to_process == 0:
                logger.info("✅ All rows already have questions generated!")
                return True

            # Apply max_rows limit if specified
            if max_rows and max_rows < rows_to_process:
                rows_needing_generation = rows_needing_generation.head(max_rows)
                rows_to_process = max_rows
                logger.info(f"📊 Limited to first {rows_to_process} empty rows")

            self.stats["total_rows"] = rows_to_process

            # Process each row that needs generation
            logger.info(f"🔄 Starting generic VQA generation for empty rows...")
            processed_count = 0

            for idx, (original_idx, row) in enumerate(
                rows_needing_generation.iterrows()
            ):
                progress = ((idx + 1) / rows_to_process) * 100
                logger.info(
                    f"🔄 Processing row {original_idx + 1} ({idx + 1}/{rows_to_process}, {progress:.1f}% complete)"
                )

                # Generate VQA for this row
                result = self._process_row(row, original_idx)

                if result:
                    # Update the dataframe with generated content
                    df.at[original_idx, "Question"] = result["question"]
                    df.at[original_idx, "Option 1"] = result["option_1"]
                    df.at[original_idx, "Option 2"] = result["option_2"]
                    df.at[original_idx, "Option 3"] = result["option_3"]
                    df.at[original_idx, "Option 4"] = result["option_4"]
                    df.at[original_idx, "Correct Option"] = result["correct_option"]

                    self.stats["processed_rows"] += 1
                    processed_count += 1
                    logger.info(f"   ✅ Row {original_idx + 1} updated successfully")
                else:
                    logger.info(f"   ⏭️  Row {original_idx + 1} skipped")

                # Save progress every 10 rows
                if (idx + 1) % 10 == 0:
                    df.to_csv(output_file, index=False)
                    logger.info(f"💾 Progress saved ({processed_count} rows updated)")

            # Final save
            df.to_csv(output_file, index=False)
            logger.info(f"💾 Final dataset saved: {output_file}")
            logger.info(f"🎯 Updated {processed_count} rows with generic VQA questions")

            # Print statistics
            self._print_statistics()

            return True

        except Exception as e:
            logger.error(f"💥 Error generating generic VQA dataset: {str(e)}")
            return False

    def _print_statistics(self):
        """Print generation statistics."""
        logger.info("=" * 60)
        logger.info("📊 Generic VQA Generation Statistics")
        logger.info("=" * 60)
        logger.info(f"Total rows: {self.stats['total_rows']}")
        logger.info(f"Successfully processed: {self.stats['processed_rows']}")
        logger.info(f"Successful generations: {self.stats['successful_generations']}")
        logger.info(
            f"Skipped (invalid keywords): {self.stats['skipped_invalid_keywords']}"
        )
        logger.info(f"API errors: {self.stats['api_errors']}")

        if self.stats["total_rows"] > 0:
            success_rate = (
                self.stats["successful_generations"] / self.stats["total_rows"]
            ) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")

        logger.info("=" * 60)

    def test_single_generation(self, keyword: str, category: str = "Test") -> bool:
        """Test generic VQA generation with a single keyword."""
        try:
            logger.info("🧪 Testing single generic VQA generation")
            logger.info(f"🎯 Keyword: {keyword}")
            logger.info(f"📂 Category: {category}")

            # Generate question
            result = self._generate_generic_question(keyword, category)

            if result:
                logger.info("✅ Test generation successful!")
                logger.info(f"❓ Question: {result['question']}")
                logger.info(f"1️⃣  Option 1: {result['option_1']}")
                logger.info(f"2️⃣  Option 2: {result['option_2']}")
                logger.info(f"3️⃣  Option 3: {result['option_3']}")
                logger.info(f"4️⃣  Option 4: {result['option_4']}")
                logger.info(f"✅ Correct: {result['correct_option']}")
                return True
            else:
                logger.error("❌ Test generation failed")
                return False

        except Exception as e:
            logger.error(f"💥 Test generation error: {str(e)}")
            return False
