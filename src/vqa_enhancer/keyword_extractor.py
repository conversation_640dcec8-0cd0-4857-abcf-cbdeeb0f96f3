"""
Keyword Extractor Module for VQA Dataset Enhancement System
Handles robust parsing and validation of LLM responses to extract clean keywords.
"""

import re
import logging
from typing import Optional, List
from .config import GENERIC_TERMS, MIN_KEYWORD_LENGTH, MAX_KEYWORD_LENGTH

logger = logging.getLogger(__name__)


class KeywordExtractor:
    """Extracts and validates keywords from LLM responses."""

    def __init__(self):
        self.generic_terms = GENERIC_TERMS

    def extract_keyword(self, llm_response: str) -> Optional[str]:
        """
        Extract clean keyword from potentially verbose LLM response.

        Args:
            llm_response: Raw response from LLM

        Returns:
            Clean keyword or None if no valid keyword found
        """
        if not llm_response or not llm_response.strip():
            logger.debug(f"     ⚠️  Empty LLM response provided")
            return None

        # Clean the response
        response = llm_response.strip()
        logger.info(f"     🔍 Extracting keyword from response: {response[:60]}...")

        # Check for explicit "NONE" response
        if response.upper() == "NONE":
            logger.info(f"     ❌ LLM explicitly returned 'NONE'")
            return None

        # Try different extraction patterns in order of preference
        logger.debug(f"     🎯 Trying extraction patterns...")
        keyword = self._try_extraction_patterns(response)

        if keyword:
            logger.info(f"     📝 Raw keyword extracted: '{keyword}'")
            # Validate the extracted keyword
            logger.debug(f"     ✅ Validating keyword...")
            if self._is_valid_keyword(keyword):
                logger.info(
                    f"     🎉 Successfully extracted valid keyword: '{keyword}'"
                )
                return keyword
            else:
                logger.warning(
                    f"     ❌ Extracted keyword '{keyword}' failed validation"
                )
        else:
            logger.warning(f"     ❌ No keyword could be extracted from response")

        return None

    def _try_extraction_patterns(self, response: str) -> Optional[str]:
        """Try multiple patterns to extract keyword from response."""

        # Pattern 1: "Title: X" format
        title_match = re.search(r"Title:\s*(.+?)(?:\n|$)", response, re.IGNORECASE)
        if title_match:
            keyword = title_match.group(1).strip()
            if keyword and keyword.upper() != "NONE":
                return self._clean_keyword(keyword)

        # Pattern 2: Quoted text
        quote_patterns = [
            r'"([^"]+)"',  # Double quotes
            r"'([^']+)'",  # Single quotes
            r"`([^`]+)`",  # Backticks
        ]

        for pattern in quote_patterns:
            matches = re.findall(pattern, response)
            for match in matches:
                keyword = match.strip()
                if keyword and keyword.upper() != "NONE":
                    cleaned = self._clean_keyword(keyword)
                    if cleaned:
                        return cleaned

        # Pattern 3: Extract from common phrases
        extraction_patterns = [
            r"(?:I suggest|I recommend|Try|Consider)(?:\s+looking up)?\s+(.+?)(?:\s+for|\s+to|\.|$)",
            r"(?:Look up)\s+(.+?)(?:\s+for|\s+to|\.|$)",
            r"(?:The answer is|Answer is)\s+(.+?)(?:\.|$)",
            r"(?:Page|Wikipedia):\s*(.+?)(?:\n|$)",
        ]

        for pattern in extraction_patterns:
            matches = re.findall(pattern, response, re.IGNORECASE)
            for match in matches:
                keyword = match.strip()
                if keyword and keyword.upper() != "NONE":
                    cleaned = self._clean_keyword(keyword)
                    if cleaned:
                        return cleaned

        # Pattern 4: Capitalized proper nouns at the end
        # Look for capitalized words that might be the answer
        lines = response.split("\n")
        for line in reversed(lines):
            line = line.strip()
            if not line:
                continue

            # Find capitalized words/phrases
            capitalized_matches = re.findall(r"\b[A-Z][a-zA-Z\s\-]+(?:\b|$)", line)
            for match in capitalized_matches:
                keyword = match.strip()
                if keyword and keyword.upper() != "NONE":
                    cleaned = self._clean_keyword(keyword)
                    if cleaned:
                        return cleaned

        # Pattern 5: Last meaningful line
        lines = [line.strip() for line in response.split("\n") if line.strip()]
        if lines:
            last_line = lines[-1]
            # Remove common prefixes
            prefixes_to_remove = [
                "Title:",
                "Answer:",
                "Keyword:",
                "Result:",
                "Page:",
                "Wikipedia:",
                "The answer is",
                "I suggest",
                "I recommend",
                "Try",
                "Consider",
            ]

            for prefix in prefixes_to_remove:
                if last_line.lower().startswith(prefix.lower()):
                    last_line = last_line[len(prefix) :].strip()
                    break

            if last_line and last_line.upper() != "NONE":
                return self._clean_keyword(last_line)

        return None

    def _clean_keyword(self, keyword: str) -> Optional[str]:
        """Clean and normalize keyword."""
        if not keyword:
            return None

        # Remove extra whitespace and normalize
        keyword = " ".join(keyword.split())

        # Remove common punctuation at the end
        keyword = re.sub(r"[.!?;,]+$", "", keyword)

        # Remove parenthetical information
        keyword = re.sub(r"\([^)]*\)", "", keyword).strip()

        # Remove quotes if they wrap the entire keyword
        if (keyword.startswith('"') and keyword.endswith('"')) or (
            keyword.startswith("'") and keyword.endswith("'")
        ):
            keyword = keyword[1:-1].strip()

        return keyword if keyword else None

    def _is_valid_keyword(self, keyword: str) -> bool:
        """Validate if keyword meets criteria."""
        if not keyword:
            return False

        # Check word count
        words = keyword.split()
        if len(words) < MIN_KEYWORD_LENGTH or len(words) > MAX_KEYWORD_LENGTH:
            logger.debug(f"Keyword '{keyword}' has invalid word count: {len(words)}")
            return False

        # Check if it's mostly proper nouns (at least one capitalized word)
        has_proper_noun = any(word[0].isupper() for word in words if word)
        if not has_proper_noun:
            logger.debug(f"Keyword '{keyword}' has no proper nouns")
            return False

        # Check for minimum meaningful length
        if len(keyword.replace(" ", "")) < 3:
            logger.debug(f"Keyword '{keyword}' is too short")
            return False

        # Smart generic term checking - only reject if ALL words are generic
        # or if the keyword is purely generic without specific proper nouns
        keyword_lower = keyword.lower()
        words_lower = [word.lower() for word in words]

        # Count how many words are generic vs specific
        generic_word_count = 0
        proper_noun_count = 0

        for i, word in enumerate(words_lower):
            if word in self.generic_terms:
                generic_word_count += 1
            elif (
                words[i][0].isupper() and len(word) > 2
            ):  # Check original case for proper noun
                proper_noun_count += 1

        # Reject if no proper nouns found
        if proper_noun_count == 0:
            logger.debug(f"Keyword '{keyword}' has no proper nouns")
            return False

        # Reject if more than half the words are generic
        if len(words) > 1 and generic_word_count > len(words) / 2:
            logger.debug(
                f"Keyword '{keyword}' is too generic: {generic_word_count}/{len(words)} generic words"
            )
            return False

        # Special case: reject standalone generic terms
        if len(words) == 1 and words_lower[0] in self.generic_terms:
            logger.debug(f"Keyword '{keyword}' is a standalone generic term")
            return False

        return True

    def validate_keywords_batch(self, keywords: List[str]) -> List[bool]:
        """Validate a batch of keywords."""
        return [self._is_valid_keyword(keyword) for keyword in keywords]
