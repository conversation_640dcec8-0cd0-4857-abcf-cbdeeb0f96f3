# Configuration file for VQA Dataset Enhancement System
import os

# Model Configuration
MODEL_NAME = "meta-llama/Llama-3.1-8B-Instruct"
DEVICE = "cuda" if os.environ.get("CUDA_VISIBLE_DEVICES") else "cpu"
MAX_LENGTH = 512
TEMPERATURE_LEVELS = [0.7, 0.85, 1.0]  # Escalating creativity levels

# Wikipedia Configuration
WIKIPEDIA_LANGUAGE = "en"
MAX_SENTENCES = 3
TIMEOUT_SECONDS = 10

# Keyword Filtering
GENERIC_TERMS = {
    "korea", "korean", "culture", "cultural", "traditional", "architecture", 
    "temple", "palace", "building", "structure", "food", "dish", "restaurant",
    "market", "shop", "store", "company", "brand", "product", "service",
    "history", "historical", "ancient", "modern", "contemporary", "style",
    "design", "art", "music", "dance", "festival", "celebration", "ceremony"
}

MIN_KEYWORD_LENGTH = 1
MAX_KEYWORD_LENGTH = 4  # Maximum number of words

# Prompting Strategy - Three escalating attempts
PROMPTS = {
    "attempt_1": """You are an expert in Korean culture and history. Analyze this Korean cultural question and its context:

Question: {question}
Korean term: {korean_term}

Generate the most specific English Wikipedia page title that would contain relevant background information about the Korean cultural concept mentioned. Focus on:
- Specific proper nouns (place names, building names, historical figures)
- Unique cultural artifacts or practices
- Historical sites or monuments
- Specific institutions or organizations

Avoid generic terms like "Korean culture", "architecture", "temple", "palace", etc.

Respond with only the specific Wikipedia page title, nothing else. If no specific term can be identified, respond with "NONE".

Title:""",

    "attempt_2": """The first attempt failed to find a specific Wikipedia page. Try alternative approaches for this Korean cultural concept:

Question: {question}
Korean term: {korean_term}

Consider:
- Alternative English names or romanizations
- Historical periods or dynasties associated with the concept
- Related geographical locations
- Different English translations
- Compound terms or specific subcategories

Focus on finding a more specific or alternative Wikipedia page title that would contain relevant information.

Respond with only the specific Wikipedia page title, nothing else. If no specific term can be identified, respond with "NONE".

Title:""",

    "attempt_3": """Final attempt. Be maximally creative and specific for this Korean cultural concept:

Question: {question}
Korean term: {korean_term}

Try unique approaches:
- Combine multiple concepts into compound terms
- Focus on very specific aspects or features
- Consider historical context or time periods
- Think about unique characteristics that distinguish this from similar concepts
- Consider alternative categorizations or classifications

This is the last chance to find a relevant Wikipedia page. Be creative but maintain accuracy.

Respond with only the specific Wikipedia page title, nothing else. If no specific term can be identified, respond with "NONE".

Title:""",
}

# Logging Configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
LOG_FILE = "vqa_enhancement.log"

# Output Configuration
OUTPUT_COLUMNS = ["keyword", "knowledge point", "knowledge point source"]
BACKUP_SUFFIX = "_backup"
