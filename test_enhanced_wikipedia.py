"""
Test script for Enhanced Wikipedia Search
Demonstrates the improved search capabilities with multiple strategies.
"""

import logging
from wikipedia_handler import WikipediaHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_search():
    """Test the enhanced Wikipedia search with various challenging cases."""
    
    handler = WikipediaHandler()
    
    # Test cases that were likely failing before
    test_cases = [
        # Cases that should work with enhanced search
        ("N Seoul Tower", "Should find with direct search"),
        ("Namsan Tower", "Alternative name for N Seoul Tower"),
        ("Seoul Tower", "Common name variant"),
        ("Gyeongbokgung Palace", "Should work with direct search"),
        ("Gyeongbokgung", "Without 'Palace' suffix"),
        ("Bulguksa Temple", "Should work with direct search"),
        ("Bulguksa", "Without 'Temple' suffix"),
        ("Cheomseongdae Observatory", "With 'Observatory' suffix"),
        ("Cheomseongdae", "Without suffix"),
        ("Jeju Stone House", "Descriptive term"),
        ("Jeju Island", "Broader geographic term"),
        ("Woljeonggyo Bridge", "With 'Bridge' suffix"),
        ("Woljeonggyo", "Without suffix"),
        ("Unhyeongung Palace", "With 'Palace' suffix"),
        ("Unhyeongung", "Without suffix"),
        ("Dongdaemun Design Plaza", "Modern landmark"),
        ("DDP", "Acronym form"),
        ("Myeongdong", "Shopping district"),
        ("Insadong", "Cultural district"),
        ("Bukchon Hanok Village", "Traditional village"),
        ("Hanok Village", "Generic term"),
        ("Changdeokgung", "Another palace"),
        ("Deoksugung", "Another palace"),
        ("Jongmyo", "Royal shrine"),
        ("Seokguram", "Buddhist grotto"),
        ("Haeinsa", "Temple with Tripitaka"),
        ("Hwaseong Fortress", "Suwon fortress"),
        ("Suwon Hwaseong", "Alternative order"),
        ("Hallasan", "Jeju mountain"),
        ("Seoraksan", "National park mountain"),
        ("Jirisan", "Another mountain"),
    ]
    
    logger.info("🧪 Testing Enhanced Wikipedia Search")
    logger.info("=" * 60)
    
    successful = 0
    failed = 0
    
    for keyword, description in test_cases:
        logger.info(f"\n🔍 Testing: '{keyword}' ({description})")
        logger.info("-" * 50)
        
        try:
            knowledge_point, source_url = handler.get_knowledge_point(keyword)
            
            if knowledge_point and source_url:
                logger.info(f"✅ SUCCESS: Found content for '{keyword}'")
                logger.info(f"   📄 Content preview: {knowledge_point[:100]}...")
                logger.info(f"   🔗 URL: {source_url}")
                successful += 1
            else:
                logger.warning(f"❌ FAILED: No content found for '{keyword}'")
                failed += 1
                
        except Exception as e:
            logger.error(f"💥 ERROR: Exception for '{keyword}': {str(e)}")
            failed += 1
    
    # Summary
    total = successful + failed
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 ENHANCED SEARCH RESULTS SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total tests: {total}")
    logger.info(f"Successful: {successful}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {success_rate:.1f}%")
    
    if success_rate > 80:
        logger.info("🎉 Excellent! Enhanced search is working very well")
    elif success_rate > 60:
        logger.info("👍 Good! Enhanced search shows significant improvement")
    elif success_rate > 40:
        logger.info("📈 Moderate improvement with enhanced search")
    else:
        logger.info("⚠️  Enhanced search needs further improvement")
    
    return success_rate

def test_specific_failures():
    """Test specific cases that were failing in the original CSV."""
    
    handler = WikipediaHandler()
    
    # Extract some failing cases from the CSV analysis
    failing_cases = [
        "N Seoul Tower",  # This was mentioned as existing but not found
        "Seoul Tower",
        "Namsan Tower",
        "DDP",
        "Dongdaemun Design Plaza",
        "Myeongdong",
        "Insadong",
        "Hanok Village",
        "Bukchon",
    ]
    
    logger.info("\n🎯 Testing Specific Previously Failing Cases")
    logger.info("=" * 50)
    
    for keyword in failing_cases:
        logger.info(f"\n🔍 Testing previously failing case: '{keyword}'")
        
        try:
            knowledge_point, source_url = handler.get_knowledge_point(keyword)
            
            if knowledge_point and source_url:
                logger.info(f"✅ NOW WORKS: '{keyword}'")
                logger.info(f"   📄 Found: {knowledge_point[:80]}...")
                logger.info(f"   🔗 URL: {source_url}")
            else:
                logger.warning(f"❌ Still failing: '{keyword}'")
                
        except Exception as e:
            logger.error(f"💥 Error: {str(e)}")

def compare_strategies():
    """Compare different search strategies on the same keyword."""
    
    handler = WikipediaHandler()
    test_keyword = "Seoul Tower"
    
    logger.info(f"\n🔬 Comparing Search Strategies for: '{test_keyword}'")
    logger.info("=" * 60)
    
    strategies = [
        ("Direct English", handler._search_english_wikipedia),
        ("Korean Wikipedia", handler._search_korean_wikipedia),
        ("Romanization Variants", handler._search_romanization_variants),
        ("Related Terms", handler._search_related_terms),
        ("Fuzzy Matching", handler._search_fuzzy_matching),
    ]
    
    for strategy_name, strategy_func in strategies:
        logger.info(f"\n🎯 Strategy: {strategy_name}")
        logger.info("-" * 30)
        
        try:
            page = strategy_func(test_keyword)
            if page:
                logger.info(f"✅ Found: {page.title}")
                logger.info(f"   🔗 URL: {page.url}")
            else:
                logger.info(f"❌ No result")
                
        except Exception as e:
            logger.error(f"💥 Error: {str(e)}")

def main():
    """Run all tests."""
    logger.info("🚀 Enhanced Wikipedia Search Testing Suite")
    logger.info("=" * 60)
    
    # Test 1: General enhanced search
    success_rate = test_enhanced_search()
    
    # Test 2: Specific failing cases
    test_specific_failures()
    
    # Test 3: Strategy comparison
    compare_strategies()
    
    logger.info("\n🎉 Testing completed!")
    logger.info(f"Overall success rate: {success_rate:.1f}%")
    
    if success_rate > 70:
        logger.info("✅ Enhanced Wikipedia search is ready for production!")
    else:
        logger.info("⚠️  Consider further improvements before production use")

if __name__ == "__main__":
    main()
