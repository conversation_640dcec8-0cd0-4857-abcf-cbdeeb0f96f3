# VQA Enhancer 🚀

A comprehensive system for enhancing and generating VQA (Visual Question Answering) datasets with Korean cultural knowledge using Wikipedia search, LLM generation, and OpenAI vision models.

## ✨ Features

### 🔍 **Enhanced Wikipedia Search**
- **Multiple search strategies**: Direct search, auto-suggest, fuzzy matching
- **Korean romanization variants**: Handles different romanization styles
- **Cultural relevance validation**: Ensures Korean cultural content
- **Fallback mechanisms**: Robust error handling and alternative searches

### 🤖 **LLM Knowledge Generation**
- **Automatic fallback**: Generates knowledge when Wikipedia search fails
- **Multi-GPU support**: Accelerated processing with multiple GPUs
- **Cultural context**: Specialized prompts for Korean cultural concepts
- **Quality control**: Content validation and length limiting

### 🔄 **Hybrid Processing Logic**
1. **Primary**: Enhanced Wikipedia search with multiple strategies
2. **Manual fallback**: Process manually provided Wikipedia URLs
3. **LLM generation**: Generate knowledge for unique terms/memes
4. **Source attribution**: Clear tracking of content sources

### 📊 **Comprehensive Analytics**
- **Detailed statistics**: Success rates by method
- **Processing logs**: Full audit trail of enhancements
- **Performance metrics**: GPU utilization and timing
- **Quality validation**: Content relevance scoring

### 🎨 **Vision-Based VQA Generation**
- **OpenAI GPT-4o-mini vision**: Advanced multimodal model for image analysis
- **Multi-hop reasoning**: Complex questions requiring cultural knowledge
- **Korean cultural focus**: Specialized prompts for Korean content
- **Automated option generation**: 4 options with plausible distractors
- **Image-dependent questions**: Unsolvable without visual context

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- CUDA-compatible GPU (recommended)
- 8GB+ RAM (16GB+ recommended for large datasets)

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd vqa-enhancer

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env and add your OPENAI_API_KEY

# Verify installation
python vqa_enhance.py info
```

## 🚀 Quick Start

### 1. Knowledge Enhancement (Wikipedia + LLM)
```bash
# Create example data
python vqa_enhance.py example

# Validate your CSV
python vqa_enhance.py validate data/vqa.csv

# Run enhancement (test mode)
python vqa_enhance.py enhance data/vqa.csv --test

# Full processing
python vqa_enhance.py enhance data/vqa.csv
```

### 2. Vision VQA Generation (OpenAI Vision)
```bash
# Test vision generation with single image
python vqa_enhance.py test-vision-generation "https://example.com/image.jpg" "한복" "Clothing"

# Generate VQA questions from images (test mode)
python vqa_enhance.py generate-vision-vqa data/VQA.csv --test

# Full vision VQA generation
python vqa_enhance.py generate-vision-vqa data/VQA.csv
```

## 📋 CSV Format Requirements

### For Knowledge Enhancement
**Required Columns:**
- **`Question`**: The VQA question text
- **`Keyword/Concept`**: Korean cultural term/concept to enhance

**Optional Columns:**
- **`knowledge point`**: Existing knowledge (will be enhanced/replaced)
- **`knowledge point source`**: Existing source URL (used as fallback)

### For Vision VQA Generation
**Required Columns:**
- **`Main Category`**: Category of the cultural content
- **`Image Source`**: URL of the image to analyze
- **`Keyword/Concept`**: Korean cultural term/concept for context

**Generated Columns:**
- **`Question`**: Generated VQA question
- **`Option 1-4`**: Four answer options
- **`Correct Option`**: Index (1-4) of correct answer

### Example CSV Structures
**Knowledge Enhancement:**
```csv
Question,Keyword/Concept,knowledge point,knowledge point source
"What is this traditional Korean palace?","Gyeongbokgung Palace","",""
"What is this Korean meme phrase?","1루수가 누구야","",""
```

**Vision VQA Generation:**
```csv
Main Category,Image Source,Keyword/Concept,Question,Option 1,Option 2,Option 3,Option 4,Correct Option
"Architecture","https://example.com/palace.jpg","Gyeongbokgung Palace","","","","","",""
"Clothing","https://example.com/hanbok.jpg","한복","","","","","",""
```

## 🎯 Usage Examples

### Knowledge Enhancement
```bash
# Basic enhancement
python vqa_enhance.py enhance data/vqa.csv

# Use specific GPUs
python vqa_enhance.py enhance data/vqa.csv --gpu_ids "0,1,2,3"

# Process limited rows
python vqa_enhance.py enhance data/vqa.csv --rows 50

# Custom output file
python vqa_enhance.py enhance data/vqa.csv data/enhanced_output.csv

# Debug mode
python vqa_enhance.py enhance data/vqa.csv --log_level DEBUG
```

### Vision VQA Generation
```bash
# Basic vision VQA generation
python vqa_enhance.py generate-vision-vqa data/VQA.csv

# Test mode (first 5 rows)
python vqa_enhance.py generate-vision-vqa data/VQA.csv --test

# Process specific number of rows
python vqa_enhance.py generate-vision-vqa data/VQA.csv --rows 20

# Custom output file
python vqa_enhance.py generate-vision-vqa data/VQA.csv data/vision_questions.csv

# Test single image generation
python vqa_enhance.py test-vision-generation "https://example.com/image.jpg" "한복" "Clothing"
```

### Multi-GPU Processing (Knowledge Enhancement)
```bash
# Use all available GPUs
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 python vqa_enhance.py enhance data/vqa.csv

# Use specific GPUs
python vqa_enhance.py enhance data/vqa.csv --gpu_ids "0,1,2,3"
```

## 🔧 Configuration

### GPU Settings
- **Automatic detection**: System automatically detects available GPUs
- **Memory optimization**: Efficient memory usage for large models
- **Batch processing**: Optimized for multi-GPU environments

### Processing Logic
1. **Wikipedia Search**: Enhanced search with multiple strategies
2. **Manual Sources**: Process manually provided Wikipedia URLs
3. **LLM Generation**: Generate knowledge for unique/cultural terms
4. **Quality Control**: Validate cultural relevance and content quality

## 📊 Output Format

The enhanced CSV will include:
- **`keyword`**: Extracted/processed keyword
- **`knowledge point`**: Enhanced knowledge content
- **`knowledge point source`**: Source URL or "generated"

### Source Types
- **Wikipedia URL**: `https://en.wikipedia.org/wiki/...`
- **Generated**: `"generated"` (for LLM-generated content)
- **Manual**: Preserved manual sources (non-Wikipedia)

## 🎯 Use Cases

### 1. **Traditional Cultural Sites**
- Palaces, temples, historical landmarks
- **Method**: Wikipedia search → High success rate
- **Example**: Gyeongbokgung Palace, Bulguksa Temple

### 2. **Modern Korean Culture**
- K-pop, dramas, modern landmarks
- **Method**: Wikipedia search → Moderate success rate
- **Example**: BTS, Gangnam Style, Lotte Tower

### 3. **Internet Memes & Slang**
- Korean internet culture, memes, catchphrases
- **Method**: LLM generation → High coverage
- **Example**: "1루수가 누구야", "무야호", "빨리빨리"

### 4. **Mixed Content**
- Datasets with diverse Korean cultural content
- **Method**: Hybrid approach → Maximum coverage
- **Result**: 90-95% success rate

## 🔍 System Information

### Check System Status
```bash
python vqa_enhance.py info
```

### Validate CSV Files
```bash
python vqa_enhance.py validate your_file.csv
```

## 📈 Performance

### Expected Processing Times
- **Single GPU**: ~2-3 minutes per row
- **Multi-GPU (8x)**: ~20-30 seconds per row
- **Wikipedia-only**: ~10-15 seconds per row

### Success Rates
- **Wikipedia search**: 30-50% (cultural landmarks)
- **LLM generation**: 90-95% (memes, unique terms)
- **Overall coverage**: 90-95% (hybrid approach)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or contributions:
1. Check existing issues
2. Create a new issue with detailed description
3. Include sample data and error logs
4. Specify your system configuration

---

**Happy enhancing! 🎉**
