# VQA Dataset Enhancement System

A sophisticated system that enhances Korean cultural VQA (Visual Question Answering) datasets by adding Wikipedia-sourced knowledge using LLM-based keyword generation.

## Overview

This system uses **Llama 3.1 8B Instruct** model with detailed prompting strategies to:
- Analyze Korean cultural questions and terms
- Generate specific English Wikipedia search terms
- Retrieve relevant knowledge points from Wikipedia
- Add structured knowledge to the dataset

## Key Features

### 🧠 Pure LLM-Based Approach
- Uses Llama 3.1 8B Instruct with detailed prompting
- NO hard-coded translations or mappings
- LLM handles Korean-to-English understanding through context

### 🔄 Iterative Wikipedia Search
- Exactly 3 attempts per row with escalating creativity
- Temperature scaling: 0.7 → 0.85 → 1.0
- Stops immediately when relevant page found

### 🎯 Smart Keyword Filtering
- Rejects generic terms (Korea, culture, architecture, etc.)
- Only accepts specific proper nouns and cultural artifacts
- 1-4 words maximum length

### 🔧 Robust Response Parsing
- Handles multiple LLM response formats
- Extracts clean keywords from verbose responses
- Removes explanations and extraneous text

## Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd vqa-enhancement-system
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up CUDA (optional but recommended):**
```bash
# For CUDA support, ensure you have appropriate PyTorch version
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Usage

### Basic Usage

```bash
python vqa_enhancement.py VQA.csv
```

### Advanced Options

```bash
# Specify output file
python vqa_enhancement.py VQA.csv -o enhanced_dataset.csv

# Resume from specific row
python vqa_enhancement.py VQA.csv -s 100

# Process limited number of rows
python vqa_enhancement.py VQA.csv -m 50

# Test mode (first 5 rows)
python vqa_enhancement.py VQA.csv --test
```

## Input/Output Format

### Input CSV Columns
- `Main Category`: Category of the question
- `Question`: English question about Korean culture
- `Option 1-4`: Multiple choice answers
- `Correct Option`: Number indicating correct answer
- `Keyword/Concept`: Korean cultural term/keyword

### Output CSV Columns (Added)
- `keyword`: English Wikipedia search term (or blank)
- `knowledge point`: First paragraph from Wikipedia (or blank)
- `knowledge point source`: Wikipedia URL (or blank)

## System Architecture

```
vqa_enhancement.py      # Main orchestrator
├── llm_handler.py      # Llama 3.1 8B integration
├── wikipedia_handler.py # Wikipedia API management
├── keyword_extractor.py # Response parsing & validation
└── config.py          # Configuration & prompts
```

## Prompting Strategy

### Attempt 1 (Temperature 0.7)
Expert analysis focusing on specific proper nouns, places, buildings, or cultural artifacts.

### Attempt 2 (Temperature 0.85)
Alternative approaches considering different romanizations, historical periods, or related concepts.

### Attempt 3 (Temperature 1.0)
Maximum creativity focusing on unique aspects, compound terms, or alternative approaches.

## Example Transformations

| Korean Term | Generated Keyword | Wikipedia Content |
|-------------|------------------|-------------------|
| 제주 돌집 | Jeju Stone House | Traditional stone houses of Jeju Island... |
| 운현궁 | Unhyeongung | Unhyeongung is a former Korean royal residence... |
| 월정교 | Woljeonggyo | Woljeonggyo is a reconstructed bridge from the Silla period... |

## Configuration

Key settings in `config.py`:

```python
# Model settings
MODEL_NAME = "meta-llama/Llama-3.1-8B-Instruct"
TEMPERATURE_LEVELS = [0.7, 0.85, 1.0]

# Filtering settings
MAX_KEYWORD_LENGTH = 4  # words
GENERIC_TERMS = {"korea", "culture", "architecture", ...}

# Wikipedia settings
MAX_SENTENCES = 3
TIMEOUT_SECONDS = 10
```

## Error Handling

- **Graceful LLM fallback**: Continues if model unavailable
- **Row-level error recovery**: Skips failed rows, continues processing
- **Progress saving**: Saves every 10 rows for resumability
- **Comprehensive logging**: Detailed logs in `vqa_enhancement.log`

## Performance Considerations

### Memory Requirements
- **GPU**: 16GB+ VRAM recommended for Llama 3.1 8B
- **CPU**: 32GB+ RAM if running on CPU
- **Storage**: ~15GB for model weights

### Processing Speed
- **GPU**: ~2-5 seconds per row
- **CPU**: ~10-30 seconds per row
- **Wikipedia API**: Rate limited automatically

## Monitoring & Logging

The system provides detailed logging including:
- Processing progress
- LLM attempt results
- Keyword extraction success/failure
- Wikipedia retrieval status
- Final statistics

## Success Criteria

The system successfully:
- ✅ Transforms Korean terms to specific English Wikipedia titles
- ✅ Rejects generic terms, leaves appropriate blanks
- ✅ Handles various LLM response formats robustly
- ✅ Provides relevant Korean cultural knowledge
- ✅ Maintains high specificity over coverage

## Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```bash
   export CUDA_VISIBLE_DEVICES=""  # Force CPU mode
   ```

2. **Model Download Issues**
   ```bash
   # Ensure sufficient disk space and stable internet
   huggingface-cli login  # If using gated models
   ```

3. **Wikipedia API Errors**
   ```bash
   # Check internet connection and try again
   # System automatically retries with backoff
   ```

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## License

[Add your license information here]

## Citation

If you use this system in your research, please cite:

```bibtex
@software{vqa_enhancement_system,
  title={VQA Dataset Enhancement System},
  author={[Your Name]},
  year={2024},
  url={[Repository URL]}
}
```
