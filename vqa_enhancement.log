2025-06-15 23:03:01,613 - INFO - Initializing VQA Enhancement System
2025-06-15 23:03:02,439 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:03:06,061 - INFO - LLM model initialized successfully
2025-06-15 23:08:54,946 - INFO - Enhancement interrupted by user
2025-06-15 23:08:54,946 - INFO - Cleaning up resources
2025-06-15 23:08:57,056 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:09:07,378 - INFO - Initializing VQA Enhancement System
2025-06-15 23:09:08,314 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:09:12,244 - INFO - LLM model initialized successfully
2025-06-15 23:09:14,408 - INFO - Enhancement interrupted by user
2025-06-15 23:09:14,409 - INFO - Cleaning up resources
2025-06-15 23:09:16,531 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:19:10,993 - INFO - 🚀 Initializing VQA Enhancement System
2025-06-15 23:19:10,993 - INFO - 📡 Step 1/3: Testing Wikipedia connection...
2025-06-15 23:19:11,812 - INFO - ✅ Wikipedia connection successful
2025-06-15 23:19:11,813 - INFO - 🧠 Step 2/3: Initializing LLM model (this may take a while)...
2025-06-15 23:19:11,813 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:19:15,466 - INFO - LLM model initialized successfully
2025-06-15 23:19:15,466 - INFO - ✅ LLM model loaded in 3.7 seconds
2025-06-15 23:19:15,466 - INFO - 🧪 Step 3/3: Testing LLM model...
2025-06-15 23:19:15,467 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:31:49,380 - INFO -      ⏱️  LLM inference completed in 753.91 seconds
2025-06-15 23:31:49,381 - INFO -      📤 Generated 2526 characters of response
2025-06-15 23:31:49,381 - INFO - 🎉 All components initialized successfully in 758.4 seconds
2025-06-15 23:31:51,649 - INFO - 📊 Model: meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:31:51,650 - INFO - 💻 Device: cpu
2025-06-15 23:31:51,651 - INFO - 🚀 CUDA devices available: 8
2025-06-15 23:31:51,651 - INFO - Loading dataset from: VQA.csv
2025-06-15 23:31:51,674 - INFO - Backup created: VQA_backup.csv
2025-06-15 23:31:51,676 - INFO - 📋 Processing rows 0 to 377 of 378
2025-06-15 23:31:51,676 - INFO - 🔄 Processing row 1/378 (0.3% complete)
2025-06-15 23:31:51,676 - INFO -    📝 Korean term: 제주 돌집
2025-06-15 23:31:51,677 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '제주 돌집'...
2025-06-15 23:31:51,677 - INFO -      🧠 Starting LLM keyword generation for '제주 돌집'
2025-06-15 23:31:51,677 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:31:51,677 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:31:51,677 - INFO -      ⏳ Generating response...
2025-06-15 23:31:51,677 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:42:39,596 - INFO - Enhancement interrupted by user
2025-06-15 23:42:39,598 - INFO - Cleaning up resources
2025-06-15 23:42:41,782 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:43:01,029 - INFO - 🚀 Initializing VQA Enhancement System
2025-06-15 23:43:01,029 - INFO - 📡 Step 1/3: Testing Wikipedia connection...
2025-06-15 23:43:02,019 - INFO - ✅ Wikipedia connection successful
2025-06-15 23:43:02,020 - INFO - 🧠 Step 2/3: Initializing LLM model (this may take a while)...
2025-06-15 23:43:03,221 - INFO - 🚀 Detected 8 GPU(s): [0, 1, 2, 3, 4, 5, 6, 7]
2025-06-15 23:43:03,222 - INFO - 🔥 Multi-GPU mode enabled with 8 GPUs
2025-06-15 23:43:03,223 - INFO - 🤖 Initializing meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:43:03,223 - INFO - 🔥 Using multi-GPU setup with 8 GPUs
2025-06-15 23:43:03,223 - INFO - 📝 Loading tokenizer...
2025-06-15 23:43:04,010 - INFO - 🔥 Loading model across 8 GPUs...
2025-06-15 23:43:04,023 - INFO -    GPU 0: 40GB allocated
2025-06-15 23:43:04,023 - INFO -    GPU 1: 40GB allocated
2025-06-15 23:43:04,023 - INFO -    GPU 2: 40GB allocated
2025-06-15 23:43:04,023 - INFO -    GPU 3: 40GB allocated
2025-06-15 23:43:04,024 - INFO -    GPU 4: 40GB allocated
2025-06-15 23:43:04,024 - INFO -    GPU 5: 40GB allocated
2025-06-15 23:43:04,024 - INFO -    GPU 6: 40GB allocated
2025-06-15 23:43:04,024 - INFO -    GPU 7: 40GB allocated
2025-06-15 23:43:11,978 - INFO - 🔧 Creating inference pipeline...
2025-06-15 23:43:11,979 - INFO - ✅ LLM model initialized successfully
2025-06-15 23:43:11,980 - INFO -    GPU 0: 1.4GB allocated, 1.4GB cached, 47.5GB total
2025-06-15 23:43:11,980 - INFO -    GPU 1: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,980 - INFO -    GPU 2: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,981 - INFO -    GPU 3: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,981 - INFO -    GPU 4: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,982 - INFO -    GPU 5: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,982 - INFO -    GPU 6: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:43:11,982 - INFO -    GPU 7: 1.4GB allocated, 1.4GB cached, 47.5GB total
2025-06-15 23:43:11,982 - INFO - ✅ LLM model loaded in 10.0 seconds
2025-06-15 23:43:11,983 - INFO - 🧪 Step 3/3: Testing LLM model...
2025-06-15 23:43:11,983 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:43:33,222 - INFO -      ⏱️  LLM inference completed in 21.24 seconds
2025-06-15 23:43:33,222 - INFO -      📤 Generated 2303 characters of response
2025-06-15 23:43:33,222 - INFO - 🎉 All components initialized successfully in 32.2 seconds
2025-06-15 23:43:33,222 - INFO - 📊 Model: meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:43:33,222 - INFO - 💻 Device: cuda
2025-06-15 23:43:33,222 - INFO - 🚀 CUDA devices available: 8
2025-06-15 23:43:33,222 - INFO - 🔥 Multi-GPU mode: Using 8 GPUs [0, 1, 2, 3, 4, 5, 6, 7]
2025-06-15 23:43:33,222 - INFO -    GPU_0: 47.5GB
2025-06-15 23:43:33,222 - INFO -    GPU_1: 47.5GB
2025-06-15 23:43:33,222 - INFO -    GPU_2: 47.5GB
2025-06-15 23:43:33,222 - INFO -    GPU_3: 47.5GB
2025-06-15 23:43:33,223 - INFO -    GPU_4: 47.5GB
2025-06-15 23:43:33,223 - INFO -    GPU_5: 47.5GB
2025-06-15 23:43:33,223 - INFO -    GPU_6: 47.5GB
2025-06-15 23:43:33,223 - INFO -    GPU_7: 47.5GB
2025-06-15 23:43:33,223 - INFO - ⚡ Expected speedup: ~8x faster inference
2025-06-15 23:43:33,223 - INFO - Loading dataset from: VQA.csv
2025-06-15 23:43:33,238 - INFO - Backup created: VQA_backup.csv
2025-06-15 23:43:33,239 - INFO - 📋 Processing rows 0 to 377 of 378
2025-06-15 23:43:33,239 - INFO - 🔄 Processing row 1/378 (0.3% complete)
2025-06-15 23:43:33,240 - INFO -    📝 Korean term: 제주 돌집
2025-06-15 23:43:33,240 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '제주 돌집'...
2025-06-15 23:43:33,240 - INFO -      🧠 Starting LLM keyword generation for '제주 돌집'
2025-06-15 23:43:33,240 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:43:33,241 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:43:33,241 - INFO -      ⏳ Generating response...
2025-06-15 23:43:33,241 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:43:51,895 - INFO -      ⏱️  LLM inference completed in 18.65 seconds
2025-06-15 23:43:51,896 - INFO -      📤 Generated 2640 characters of response
2025-06-15 23:43:51,896 - INFO -      ✅ LLM attempt 1 generated: Jeju Stone House Architecture

Explanation: The Jeju Stone House (제주 돌집) is a tr...
2025-06-15 23:43:51,897 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:43:51,897 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:43:51,897 - INFO -      🔍 Extracting keyword from response: Jeju Stone House Architecture

Explanation: The Jeju Stone H...
2025-06-15 23:43:51,897 - INFO -      📝 Raw keyword extracted: 'Jeju Stone House Architecture'
2025-06-15 23:43:51,897 - INFO -      🎉 Successfully extracted valid keyword: 'Jeju Stone House Architecture'
2025-06-15 23:43:51,897 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Jeju Stone House Architecture'...
2025-06-15 23:43:51,897 - INFO -      🔍 Searching Wikipedia for: 'Jeju Stone House Architecture'
2025-06-15 23:43:51,898 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:43:54,114 - INFO -        ✅ Auto-suggest found: 'Choga (architecture)'
2025-06-15 23:43:54,114 - INFO -      ✅ Found page: 'Choga (architecture)'
2025-06-15 23:43:54,114 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:43:54,840 - INFO -      📏 Extracted 199 characters of content
2025-06-15 23:43:54,840 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:43:54,840 - INFO -      🎉 Successfully retrieved knowledge from: Choga (architecture)
2025-06-15 23:43:54,841 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Choga_(architecture)
2025-06-15 23:43:54,841 - INFO -    🎉 Row 0: Successfully enhanced! '제주 돌집' → 'Jeju Stone House Architecture'
2025-06-15 23:43:54,841 - INFO -    📄 Knowledge preview: Choga (Korean: 초가; Hanja: 草家; lit. grass house) is a term for traditional Korean...
2025-06-15 23:43:54,842 - INFO -    ✅ Row 1 enhanced successfully with keyword: Jeju Stone House Architecture (21.6s)
2025-06-15 23:43:54,842 - INFO - 🔄 Processing row 2/378 (0.5% complete, ETA: 2.3h)
2025-06-15 23:43:54,843 - INFO -    📝 Korean term: 월정교
2025-06-15 23:43:54,843 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '월정교'...
2025-06-15 23:43:54,843 - INFO -      🧠 Starting LLM keyword generation for '월정교'
2025-06-15 23:43:54,843 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:43:54,843 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:43:54,843 - INFO -      ⏳ Generating response...
2025-06-15 23:43:54,843 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:44:13,668 - INFO -      ⏱️  LLM inference completed in 18.82 seconds
2025-06-15 23:44:13,668 - INFO -      📤 Generated 1646 characters of response
2025-06-15 23:44:13,668 - INFO -      ✅ LLM attempt 1 generated: Cheomseongdae Observatory, Gyeongju Historic Area, Silla Kingdom, Silla-era arch...
2025-06-15 23:44:13,669 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:44:13,669 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:44:13,669 - INFO -      🔍 Extracting keyword from response: Cheomseongdae Observatory, Gyeongju Historic Area, Silla Kin...
2025-06-15 23:44:13,670 - INFO -      📝 Raw keyword extracted: 'Cheomseongdae Observatory'
2025-06-15 23:44:13,670 - INFO -      🎉 Successfully extracted valid keyword: 'Cheomseongdae Observatory'
2025-06-15 23:44:13,671 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Cheomseongdae Observatory'...
2025-06-15 23:44:13,671 - INFO -      🔍 Searching Wikipedia for: 'Cheomseongdae Observatory'
2025-06-15 23:44:13,671 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:44:15,852 - INFO -        ✅ Auto-suggest found: 'Cheomseongdae'
2025-06-15 23:44:15,852 - INFO -      ✅ Found page: 'Cheomseongdae'
2025-06-15 23:44:15,852 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:44:16,518 - INFO -      📏 Extracted 221 characters of content
2025-06-15 23:44:16,518 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:44:16,518 - INFO -      🎉 Successfully retrieved knowledge from: Cheomseongdae
2025-06-15 23:44:16,518 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Cheomseongdae
2025-06-15 23:44:16,519 - INFO -    🎉 Row 1: Successfully enhanced! '월정교' → 'Cheomseongdae Observatory'
2025-06-15 23:44:16,519 - INFO -    📄 Knowledge preview: Cheomseongdae (Korean: 첨성대; Hanja: 瞻星臺; lit. star-gazing tower) is an astronomic...
2025-06-15 23:44:16,520 - INFO -    ✅ Row 2 enhanced successfully with keyword: Cheomseongdae Observatory (21.7s)
2025-06-15 23:44:16,520 - INFO - 🔄 Processing row 3/378 (0.8% complete, ETA: 2.3h)
2025-06-15 23:44:16,521 - INFO -    📝 Korean term: 운현궁
2025-06-15 23:44:16,521 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '운현궁'...
2025-06-15 23:44:16,521 - INFO -      🧠 Starting LLM keyword generation for '운현궁'
2025-06-15 23:44:16,521 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:44:16,521 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:44:16,522 - INFO -      ⏳ Generating response...
2025-06-15 23:44:16,522 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:44:35,247 - INFO -      ⏱️  LLM inference completed in 18.73 seconds
2025-06-15 23:44:35,247 - INFO -      📤 Generated 2274 characters of response
2025-06-15 23:44:35,247 - INFO -      ✅ LLM attempt 1 generated: Ungjin Palace (Ungjin Palace)

This smaller palace, Ungjin Palace, was the resid...
2025-06-15 23:44:35,247 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:44:35,247 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:44:35,247 - INFO -      🔍 Extracting keyword from response: Ungjin Palace (Ungjin Palace)

This smaller palace, Ungjin P...
2025-06-15 23:44:35,248 - INFO -      📝 Raw keyword extracted: 'Ungjin Palace'
2025-06-15 23:44:35,248 - INFO -      🎉 Successfully extracted valid keyword: 'Ungjin Palace'
2025-06-15 23:44:35,248 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Ungjin Palace'...
2025-06-15 23:44:35,248 - INFO -      🔍 Searching Wikipedia for: 'Ungjin Palace'
2025-06-15 23:44:35,248 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:44:37,539 - INFO -        ✅ Auto-suggest found: 'Ungjin Commandery'
2025-06-15 23:44:37,540 - INFO -      ✅ Found page: 'Ungjin Commandery'
2025-06-15 23:44:37,540 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:44:38,218 - INFO -      📏 Extracted 468 characters of content
2025-06-15 23:44:38,218 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:44:38,218 - INFO -      🎉 Successfully retrieved knowledge from: Ungjin Commandery
2025-06-15 23:44:38,218 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Ungjin_Commandery
2025-06-15 23:44:38,219 - INFO -    🎉 Row 2: Successfully enhanced! '운현궁' → 'Ungjin Palace'
2025-06-15 23:44:38,219 - INFO -    📄 Knowledge preview: The Ungjin Commandery was an administrative division of the Chinese Tang dynasty...
2025-06-15 23:44:38,220 - INFO -    ✅ Row 3 enhanced successfully with keyword: Ungjin Palace (21.7s)
2025-06-15 23:44:38,220 - INFO - 🔄 Processing row 4/378 (1.1% complete, ETA: 2.3h)
2025-06-15 23:44:38,221 - INFO -    📝 Korean term: 명동
2025-06-15 23:44:38,221 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '명동'...
2025-06-15 23:44:38,221 - INFO -      🧠 Starting LLM keyword generation for '명동'
2025-06-15 23:44:38,221 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:44:38,221 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:44:38,221 - INFO -      ⏳ Generating response...
2025-06-15 23:44:38,221 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:44:56,945 - INFO -      ⏱️  LLM inference completed in 18.72 seconds
2025-06-15 23:44:56,945 - INFO -      📤 Generated 2714 characters of response
2025-06-15 23:44:56,945 - INFO -      ✅ LLM attempt 1 generated: Myeong-dong shopping district

Explanation: The Myeong-dong shopping district is...
2025-06-15 23:44:56,946 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:44:56,946 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:44:56,946 - INFO -      🔍 Extracting keyword from response: Myeong-dong shopping district

Explanation: The Myeong-dong ...
2025-06-15 23:44:56,946 - INFO -      📝 Raw keyword extracted: 'Myeong-dong shopping district'
2025-06-15 23:44:56,946 - INFO -      🎉 Successfully extracted valid keyword: 'Myeong-dong shopping district'
2025-06-15 23:44:56,946 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Myeong-dong shopping district'...
2025-06-15 23:44:56,946 - INFO -      🔍 Searching Wikipedia for: 'Myeong-dong shopping district'
2025-06-15 23:44:56,946 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:44:59,269 - INFO -        ✅ Auto-suggest found: 'Myeong-dong'
2025-06-15 23:44:59,269 - INFO -      ✅ Found page: 'Myeong-dong'
2025-06-15 23:44:59,270 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:44:59,898 - INFO -      📏 Extracted 259 characters of content
2025-06-15 23:44:59,898 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:44:59,899 - INFO -      🎉 Successfully retrieved knowledge from: Myeong-dong
2025-06-15 23:44:59,900 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Myeong-dong
2025-06-15 23:44:59,900 - INFO -    🎉 Row 3: Successfully enhanced! '명동' → 'Myeong-dong shopping district'
2025-06-15 23:44:59,901 - INFO -    📄 Knowledge preview: Myeong-dong (Korean: 명동; lit. 'bright neighborhood') is a dong (neighborhood) in...
2025-06-15 23:44:59,902 - INFO -    ✅ Row 4 enhanced successfully with keyword: Myeong-dong shopping district (21.7s)
2025-06-15 23:44:59,902 - INFO - 🔄 Processing row 5/378 (1.3% complete, ETA: 2.2h)
2025-06-15 23:44:59,904 - INFO -    📝 Korean term: 남산타워
2025-06-15 23:44:59,904 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '남산타워'...
2025-06-15 23:44:59,904 - INFO -      🧠 Starting LLM keyword generation for '남산타워'
2025-06-15 23:44:59,905 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:44:59,905 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:44:59,905 - INFO -      ⏳ Generating response...
2025-06-15 23:44:59,906 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:45:18,765 - INFO -      ⏱️  LLM inference completed in 18.86 seconds
2025-06-15 23:45:18,765 - INFO -      📤 Generated 2408 characters of response
2025-06-15 23:45:18,765 - INFO -      ✅ LLM attempt 1 generated: N Seoul Tower

Note: The answer should be in the form of a specific English Wiki...
2025-06-15 23:45:18,765 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:45:18,766 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:45:18,766 - INFO -      🔍 Extracting keyword from response: N Seoul Tower

Note: The answer should be in the form of a s...
2025-06-15 23:45:18,767 - INFO -      📝 Raw keyword extracted: 's the case, I'
2025-06-15 23:45:18,767 - WARNING -      ❌ Extracted keyword 's the case, I' failed validation
2025-06-15 23:45:18,767 - WARNING -    ❌ Row 4: Failed to extract valid keyword from: N Seoul Tower

Note: The answer should be in the f...
2025-06-15 23:45:18,767 - INFO -    ❌ Row 5 enhancement failed (18.9s)
2025-06-15 23:45:18,767 - INFO - 💓 Heartbeat: 5 rows processed, avg 21.1s/row
2025-06-15 23:45:18,767 - INFO - 🔄 Processing row 6/378 (1.6% complete, ETA: 2.2h)
2025-06-15 23:45:18,768 - INFO -    📝 Korean term: 신라대종
2025-06-15 23:45:18,768 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '신라대종'...
2025-06-15 23:45:18,768 - INFO -      🧠 Starting LLM keyword generation for '신라대종'
2025-06-15 23:45:18,769 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:45:18,769 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:45:18,769 - INFO -      ⏳ Generating response...
2025-06-15 23:45:18,769 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:45:37,398 - INFO -      ⏱️  LLM inference completed in 18.63 seconds
2025-06-15 23:45:37,399 - INFO -      📤 Generated 2322 characters of response
2025-06-15 23:45:37,399 - INFO -      ✅ LLM attempt 1 generated: "Daeungjeon Hall" is not relevant to this question. 
Title: "Seokguram Grotto" i...
2025-06-15 23:45:37,399 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:45:37,400 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:45:37,400 - INFO -      🔍 Extracting keyword from response: "Daeungjeon Hall" is not relevant to this question. 
Title: ...
2025-06-15 23:45:37,400 - INFO -      📝 Raw keyword extracted: '"Seokguram Grotto" is not directly related to the topic'
2025-06-15 23:45:37,400 - WARNING -      ❌ Extracted keyword '"Seokguram Grotto" is not directly related to the topic' failed validation
2025-06-15 23:45:37,400 - WARNING -    ❌ Row 5: Failed to extract valid keyword from: "Daeungjeon Hall" is not relevant to this question...
2025-06-15 23:45:37,400 - INFO -    ❌ Row 6 enhancement failed (18.6s)
2025-06-15 23:45:37,401 - INFO - 🔄 Processing row 7/378 (1.9% complete, ETA: 2.1h)
2025-06-15 23:45:37,401 - INFO -    📝 Korean term: 고려대학교
2025-06-15 23:45:37,401 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '고려대학교'...
2025-06-15 23:45:37,401 - INFO -      🧠 Starting LLM keyword generation for '고려대학교'
2025-06-15 23:45:37,401 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:45:37,402 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:45:37,402 - INFO -      ⏳ Generating response...
2025-06-15 23:45:37,402 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:45:56,000 - INFO -      ⏱️  LLM inference completed in 18.60 seconds
2025-06-15 23:45:56,000 - INFO -      📤 Generated 2500 characters of response
2025-06-15 23:45:56,001 - INFO -      ✅ LLM attempt 1 generated: Sogang University campus#Architecture

This title will be used to provide backgr...
2025-06-15 23:45:56,001 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:45:56,001 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:45:56,001 - INFO -      🔍 Extracting keyword from response: Sogang University campus#Architecture

This title will be us...
2025-06-15 23:45:56,002 - INFO -      📝 Raw keyword extracted: 'Sogang University campus#Architecture'
2025-06-15 23:45:56,002 - INFO -      🎉 Successfully extracted valid keyword: 'Sogang University campus#Architecture'
2025-06-15 23:45:56,002 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Sogang University campus#Architecture'...
2025-06-15 23:45:56,002 - INFO -      🔍 Searching Wikipedia for: 'Sogang University campus#Architecture'
2025-06-15 23:45:56,002 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:45:58,344 - INFO -        ✅ Auto-suggest found: 'Fu Jen Catholic University'
2025-06-15 23:45:58,344 - INFO -      ✅ Found page: 'Fu Jen Catholic University'
2025-06-15 23:45:58,344 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:45:59,142 - INFO -      📏 Extracted 492 characters of content
2025-06-15 23:45:59,142 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:45:59,142 - WARNING -      ❌ Content not relevant to Korean culture: Fu Jen Catholic University
2025-06-15 23:45:59,143 - WARNING -    ❌ Row 6: Failed to get Wikipedia content for: Sogang University campus#Architecture
2025-06-15 23:45:59,143 - INFO -    ❌ Row 7 enhancement failed (21.7s)
2025-06-15 23:45:59,143 - INFO - 🔄 Processing row 8/378 (2.1% complete, ETA: 2.1h)
2025-06-15 23:45:59,144 - INFO -    📝 Korean term: 한강다리
2025-06-15 23:45:59,145 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '한강다리'...
2025-06-15 23:45:59,145 - INFO -      🧠 Starting LLM keyword generation for '한강다리'
2025-06-15 23:45:59,145 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:45:59,146 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:45:59,146 - INFO -      ⏳ Generating response...
2025-06-15 23:45:59,146 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:46:17,894 - INFO -      ⏱️  LLM inference completed in 18.75 seconds
2025-06-15 23:46:17,894 - INFO -      📤 Generated 2267 characters of response
2025-06-15 23:46:17,894 - INFO -      ✅ LLM attempt 1 generated: "Yongjong Bridge" (NONE) -> Yongjong Bridge is a specific bridge, but not the Ko...
2025-06-15 23:46:17,895 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:46:17,895 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:46:17,895 - INFO -      🔍 Extracting keyword from response: "Yongjong Bridge" (NONE) -> Yongjong Bridge is a specific br...
2025-06-15 23:46:17,895 - INFO -      📝 Raw keyword extracted: 'Yongjong Bridge'
2025-06-15 23:46:17,895 - INFO -      🎉 Successfully extracted valid keyword: 'Yongjong Bridge'
2025-06-15 23:46:17,895 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Yongjong Bridge'...
2025-06-15 23:46:17,896 - INFO -      🔍 Searching Wikipedia for: 'Yongjong Bridge'
2025-06-15 23:46:17,896 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:46:21,107 - INFO -        📋 Search returned 1 results: ['Yeongjongdo']
2025-06-15 23:46:21,788 - INFO -        ✅ Search result found: 'Yeongjongdo'
2025-06-15 23:46:21,788 - INFO -      ✅ Found page: 'Yeongjongdo'
2025-06-15 23:46:21,788 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:46:22,470 - INFO -      📏 Extracted 340 characters of content
2025-06-15 23:46:22,470 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:46:22,470 - INFO -      🎉 Successfully retrieved knowledge from: Yeongjongdo
2025-06-15 23:46:22,471 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Yeongjongdo
2025-06-15 23:46:22,471 - INFO -    🎉 Row 7: Successfully enhanced! '한강다리' → 'Yongjong Bridge'
2025-06-15 23:46:22,471 - INFO -    📄 Knowledge preview: Yeongjong Island (Korean: 영종도) is an island in Jung District, Incheon, South Kor...
2025-06-15 23:46:22,471 - INFO -    ✅ Row 8 enhanced successfully with keyword: Yongjong Bridge (23.3s)
2025-06-15 23:46:22,472 - INFO - 🔄 Processing row 9/378 (2.4% complete, ETA: 2.2h)
2025-06-15 23:46:22,473 - INFO -    📝 Korean term: DDP
2025-06-15 23:46:22,473 - INFO -    🤖 Step 1/3: Generating keywords using LLM for 'DDP'...
2025-06-15 23:46:22,473 - INFO -      🧠 Starting LLM keyword generation for 'DDP'
2025-06-15 23:46:22,473 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:46:22,473 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:46:22,473 - INFO -      ⏳ Generating response...
2025-06-15 23:46:22,473 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:46:41,198 - INFO -      ⏱️  LLM inference completed in 18.73 seconds
2025-06-15 23:46:41,199 - INFO -      📤 Generated 2309 characters of response
2025-06-15 23:46:41,199 - INFO -      ✅ LLM attempt 1 generated: Dongdaemun Design Plaza (DDP) Wikipedia page title.  https://en.wikipedia.org/wi...
2025-06-15 23:46:41,199 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:46:41,199 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:46:41,199 - INFO -      🔍 Extracting keyword from response: Dongdaemun Design Plaza (DDP) Wikipedia page title.  https:/...
2025-06-15 23:46:41,200 - INFO -      📝 Raw keyword extracted: 'Dongdaemun Design Park'
2025-06-15 23:46:41,200 - INFO -      🎉 Successfully extracted valid keyword: 'Dongdaemun Design Park'
2025-06-15 23:46:41,200 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Dongdaemun Design Park'...
2025-06-15 23:46:41,200 - INFO -      🔍 Searching Wikipedia for: 'Dongdaemun Design Park'
2025-06-15 23:46:41,200 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:46:43,409 - INFO -        ✅ Auto-suggest found: 'Dongdaemun Design Plaza'
2025-06-15 23:46:43,409 - INFO -      ✅ Found page: 'Dongdaemun Design Plaza'
2025-06-15 23:46:43,409 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:46:44,043 - INFO -      📏 Extracted 608 characters of content
2025-06-15 23:46:44,043 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:46:44,043 - INFO -      🎉 Successfully retrieved knowledge from: Dongdaemun Design Plaza
2025-06-15 23:46:44,043 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Dongdaemun_Design_Plaza
2025-06-15 23:46:44,043 - INFO -    🎉 Row 8: Successfully enhanced! 'DDP' → 'Dongdaemun Design Park'
2025-06-15 23:46:44,043 - INFO -    📄 Knowledge preview: Dongdaemun Design Plaza (DDP; Korean: 동대문 디자인 플라자) is a major urban development ...
2025-06-15 23:46:44,044 - INFO -    ✅ Row 9 enhanced successfully with keyword: Dongdaemun Design Park (21.6s)
2025-06-15 23:46:44,044 - INFO - 🔄 Processing row 10/378 (2.6% complete, ETA: 2.2h)
2025-06-15 23:46:44,044 - INFO -    📝 Korean term: 탑골공원
2025-06-15 23:46:44,045 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '탑골공원'...
2025-06-15 23:46:44,045 - INFO -      🧠 Starting LLM keyword generation for '탑골공원'
2025-06-15 23:46:44,045 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:46:44,045 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:46:44,045 - INFO -      ⏳ Generating response...
2025-06-15 23:46:44,045 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:47:02,738 - INFO -      ⏱️  LLM inference completed in 18.69 seconds
2025-06-15 23:47:02,738 - INFO -      📤 Generated 2211 characters of response
2025-06-15 23:47:02,738 - INFO -      ✅ LLM attempt 1 generated: "Tapgol Park"  (redirects to: Tapgol Park) 

Note: In the context of the questio...
2025-06-15 23:47:02,739 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:47:02,739 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:47:02,739 - INFO -      🔍 Extracting keyword from response: "Tapgol Park"  (redirects to: Tapgol Park) 

Note: In the co...
2025-06-15 23:47:02,739 - INFO -      📝 Raw keyword extracted: 'Tapgol Park'
2025-06-15 23:47:02,739 - INFO -      🎉 Successfully extracted valid keyword: 'Tapgol Park'
2025-06-15 23:47:02,739 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Tapgol Park'...
2025-06-15 23:47:02,739 - INFO -      🔍 Searching Wikipedia for: 'Tapgol Park'
2025-06-15 23:47:02,739 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:47:03,340 - INFO -        ✅ Exact match found: 'Tapgol Park'
2025-06-15 23:47:03,340 - INFO -      ✅ Found page: 'Tapgol Park'
2025-06-15 23:47:03,340 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:47:03,937 - INFO -      📏 Extracted 148 characters of content
2025-06-15 23:47:03,937 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:47:03,937 - INFO -      🎉 Successfully retrieved knowledge from: Tapgol Park
2025-06-15 23:47:03,937 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Tapgol_Park
2025-06-15 23:47:03,938 - INFO -    🎉 Row 9: Successfully enhanced! '탑골공원' → 'Tapgol Park'
2025-06-15 23:47:03,938 - INFO -    📄 Knowledge preview: Tapgol Park (Korean: 탑골 공원), formerly Pagoda Park, is a public park located at 9...
2025-06-15 23:47:03,938 - INFO -    ✅ Row 10 enhanced successfully with keyword: Tapgol Park (19.9s)
2025-06-15 23:47:03,939 - INFO - 💓 Heartbeat: 10 rows processed, avg 21.1s/row
2025-06-15 23:47:03,939 - INFO - 💾 Saving progress at row 10...
2025-06-15 23:47:03,947 - INFO -    📊 Current success rate: 70.0% (7/10 rows)
2025-06-15 23:47:03,947 - INFO -    ⏱️  Total elapsed time: 3.5 minutes
2025-06-15 23:47:03,947 - INFO - 🔄 Processing row 11/378 (2.9% complete, ETA: 2.1h)
2025-06-15 23:47:03,948 - INFO -    📝 Korean term: PC방
2025-06-15 23:47:03,948 - INFO -    🤖 Step 1/3: Generating keywords using LLM for 'PC방'...
2025-06-15 23:47:03,948 - INFO -      🧠 Starting LLM keyword generation for 'PC방'
2025-06-15 23:47:03,948 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:47:03,948 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:47:03,948 - INFO -      ⏳ Generating response...
2025-06-15 23:47:03,948 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:47:07,068 - INFO -      ⏱️  LLM inference completed in 3.12 seconds
2025-06-15 23:47:07,068 - INFO -      📤 Generated 423 characters of response
2025-06-15 23:47:07,068 - INFO -      ✅ LLM attempt 1 generated: Computer gaming in South Korea, PC bang culture, and the rise of E-Sports in Seo...
2025-06-15 23:47:07,069 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:47:07,069 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:47:07,069 - INFO -      🔍 Extracting keyword from response: Computer gaming in South Korea, PC bang culture, and the ris...
2025-06-15 23:47:07,069 - INFO -      📝 Raw keyword extracted: 'The final answer is'
2025-06-15 23:47:07,069 - INFO -      🎉 Successfully extracted valid keyword: 'The final answer is'
2025-06-15 23:47:07,069 - INFO -    📚 Step 3/3: Searching Wikipedia for 'The final answer is'...
2025-06-15 23:47:07,069 - INFO -      🔍 Searching Wikipedia for: 'The final answer is'
2025-06-15 23:47:07,069 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:47:09,500 - INFO -        ✅ Auto-suggest found: 'International versions of Who Wants to Be a Millionaire?'
2025-06-15 23:47:09,500 - INFO -      ✅ Found page: 'International versions of Who Wants to Be a Millionaire?'
2025-06-15 23:47:09,500 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:47:10,085 - INFO -      📏 Extracted 218 characters of content
2025-06-15 23:47:10,085 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:47:10,085 - WARNING -      ❌ Content not relevant to Korean culture: International versions of Who Wants to Be a Millionaire?
2025-06-15 23:47:10,086 - WARNING -    ❌ Row 10: Failed to get Wikipedia content for: The final answer is
2025-06-15 23:47:10,086 - INFO -    ❌ Row 11 enhancement failed (6.1s)
2025-06-15 23:47:10,086 - INFO - 🔄 Processing row 12/378 (3.2% complete, ETA: 2.0h)
2025-06-15 23:47:10,087 - INFO -    📝 Korean term: 분식집
2025-06-15 23:47:10,087 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '분식집'...
2025-06-15 23:47:10,088 - INFO -      🧠 Starting LLM keyword generation for '분식집'
2025-06-15 23:47:10,088 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:47:10,088 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:47:10,088 - INFO -      ⏳ Generating response...
2025-06-15 23:47:10,088 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:47:28,779 - INFO -      ⏱️  LLM inference completed in 18.69 seconds
2025-06-15 23:47:28,780 - INFO -      📤 Generated 1858 characters of response
2025-06-15 23:47:28,780 - INFO -      ✅ LLM attempt 1 generated: Korean Street Food - Jjimdak and Bungeo-ppang, a popular Korean street food in S...
2025-06-15 23:47:28,781 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:47:28,781 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:47:28,781 - INFO -      🔍 Extracting keyword from response: Korean Street Food - Jjimdak and Bungeo-ppang, a popular Kor...
2025-06-15 23:47:28,781 - INFO -      📝 Raw keyword extracted: 'Korean Street Food - Bungeo-ppang and Tteokbokki, popular Korean street food in Myeong-dong, Seoul'
2025-06-15 23:47:28,781 - WARNING -      ❌ Extracted keyword 'Korean Street Food - Bungeo-ppang and Tteokbokki, popular Korean street food in Myeong-dong, Seoul' failed validation
2025-06-15 23:47:28,781 - WARNING -    ❌ Row 11: Failed to extract valid keyword from: Korean Street Food - Jjimdak and Bungeo-ppang, a p...
2025-06-15 23:47:28,782 - INFO -    ❌ Row 12 enhancement failed (18.7s)
2025-06-15 23:47:28,782 - INFO - 🔄 Processing row 13/378 (3.4% complete, ETA: 1.9h)
2025-06-15 23:47:28,782 - INFO -    📝 Korean term: 빵집
2025-06-15 23:47:28,783 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '빵집'...
2025-06-15 23:47:28,783 - INFO -      🧠 Starting LLM keyword generation for '빵집'
2025-06-15 23:47:28,783 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:47:28,783 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:47:28,783 - INFO -      ⏳ Generating response...
2025-06-15 23:47:28,783 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:47:47,535 - INFO -      ⏱️  LLM inference completed in 18.75 seconds
2025-06-15 23:47:47,536 - INFO -      📤 Generated 2948 characters of response
2025-06-15 23:47:47,536 - INFO -      ✅ LLM attempt 1 generated: Korean Bakery Industry and European Influences on Local Breads (Bakery Industry ...
2025-06-15 23:47:47,536 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:47:47,536 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:47:47,536 - INFO -      🔍 Extracting keyword from response: Korean Bakery Industry and European Influences on Local Brea...
2025-06-15 23:47:47,537 - INFO -      📝 Raw keyword extracted: 'and European Influences on Local Breads  - European Bakery Techniques in Korean Bakery Industry - Influence of Western Baking on Korean Bakery - Korean Bakery Evolution and European Influence - Korean Bakeries and European Influence - Korean Bakery Industry and Western Baking Traditions - European Bakery Techniques in Korean Bakeries - Korean Bakery Industry and Adaptation of Western Baking - Korean Bakeries and European Influence on Local Breads - Korean Bakery Evolution under European Influence - Korean Bakeries and Western Influence - Korean Bakery Industry and European Influence on Traditional Breads - Korean Bakeries and Adaptation of European Baking Techniques - Korean Bakery Industry and Western Bakery Techniques - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and European Influence on Korean Breads - Korean Bakeries and Western Influence on Traditional Korean Breads - Korean Bakery Industry and European Influence on Local Korean Breads - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and Adaptation of European Baking - Korean Bakeries and European Influence on Traditional Korean Pastries - Korean Bakery Industry and Western Baking Influence - Korean Bakeries and European Influence on Korean Traditional Breads - Korean Bakery Industry and European Influence on Korean Local Breads - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and Adaptation of Western Baking Techniques - Korean Bakeries and European Influence on Korean Traditional Pastries - Korean Bakery Industry and European Influence on Korean Local Pastries - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and European Influence on Korean Local Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Local Baked Goods - Korean Bakery Industry and European Influence on Korean Traditional Baked Goods - Korean Bakeries and European Influence on Korean Local Baked Goods - Korean Bakery Industry and European Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods - Korean Bakery Industry and European Influence on Korean Traditional Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods - Korean Bakery Industry and European Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods'
2025-06-15 23:47:47,537 - WARNING -      ❌ Extracted keyword 'and European Influences on Local Breads  - European Bakery Techniques in Korean Bakery Industry - Influence of Western Baking on Korean Bakery - Korean Bakery Evolution and European Influence - Korean Bakeries and European Influence - Korean Bakery Industry and Western Baking Traditions - European Bakery Techniques in Korean Bakeries - Korean Bakery Industry and Adaptation of Western Baking - Korean Bakeries and European Influence on Local Breads - Korean Bakery Evolution under European Influence - Korean Bakeries and Western Influence - Korean Bakery Industry and European Influence on Traditional Breads - Korean Bakeries and Adaptation of European Baking Techniques - Korean Bakery Industry and Western Bakery Techniques - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and European Influence on Korean Breads - Korean Bakeries and Western Influence on Traditional Korean Breads - Korean Bakery Industry and European Influence on Local Korean Breads - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and Adaptation of European Baking - Korean Bakeries and European Influence on Traditional Korean Pastries - Korean Bakery Industry and Western Baking Influence - Korean Bakeries and European Influence on Korean Traditional Breads - Korean Bakery Industry and European Influence on Korean Local Breads - Korean Bakeries and European Influence on Traditional Korean Breads - Korean Bakery Industry and Adaptation of Western Baking Techniques - Korean Bakeries and European Influence on Korean Traditional Pastries - Korean Bakery Industry and European Influence on Korean Local Pastries - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and European Influence on Korean Local Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Local Baked Goods - Korean Bakery Industry and European Influence on Korean Traditional Baked Goods - Korean Bakeries and European Influence on Korean Local Baked Goods - Korean Bakery Industry and European Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Traditional Baked Goods - Korean Bakery Industry and Western Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods - Korean Bakery Industry and European Influence on Korean Traditional Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods - Korean Bakery Industry and European Influence on Korean Baked Goods - Korean Bakeries and European Influence on Korean Baked Goods' failed validation
2025-06-15 23:47:47,537 - WARNING -    ❌ Row 12: Failed to extract valid keyword from: Korean Bakery Industry and European Influences on ...
2025-06-15 23:47:47,537 - INFO -    ❌ Row 13 enhancement failed (18.8s)
2025-06-15 23:47:47,537 - INFO - 🔄 Processing row 14/378 (3.7% complete, ETA: 1.9h)
2025-06-15 23:47:47,538 - INFO -    📝 Korean term: 광화문
2025-06-15 23:47:47,538 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '광화문'...
2025-06-15 23:47:47,538 - INFO -      🧠 Starting LLM keyword generation for '광화문'
2025-06-15 23:47:47,538 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:47:47,538 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:47:47,538 - INFO -      ⏳ Generating response...
2025-06-15 23:47:47,539 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:48:06,262 - INFO -      ⏱️  LLM inference completed in 18.72 seconds
2025-06-15 23:48:06,262 - INFO -      📤 Generated 2459 characters of response
2025-06-15 23:48:06,262 - INFO -      ✅ LLM attempt 1 generated: Gwanghwamun Gate


Explanation:

The Gwanghwamun Gate is a significant historica...
2025-06-15 23:48:06,263 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:48:06,263 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:48:06,263 - INFO -      🔍 Extracting keyword from response: Gwanghwamun Gate


Explanation:

The Gwanghwamun Gate is a s...
2025-06-15 23:48:06,263 - INFO -      📝 Raw keyword extracted: 'Gwanghwamun Gate'
2025-06-15 23:48:06,263 - INFO -      🎉 Successfully extracted valid keyword: 'Gwanghwamun Gate'
2025-06-15 23:48:06,263 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Gwanghwamun Gate'...
2025-06-15 23:48:06,263 - INFO -      🔍 Searching Wikipedia for: 'Gwanghwamun Gate'
2025-06-15 23:48:06,263 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:48:07,462 - INFO -        ✅ Exact match found: 'Gwanghwamun'
2025-06-15 23:48:07,462 - INFO -      ✅ Found page: 'Gwanghwamun'
2025-06-15 23:48:07,462 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:48:08,089 - INFO -      📏 Extracted 351 characters of content
2025-06-15 23:48:08,089 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:48:08,089 - INFO -      🎉 Successfully retrieved knowledge from: Gwanghwamun
2025-06-15 23:48:08,090 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Gwanghwamun
2025-06-15 23:48:08,090 - INFO -    🎉 Row 13: Successfully enhanced! '광화문' → 'Gwanghwamun Gate'
2025-06-15 23:48:08,090 - INFO -    📄 Knowledge preview: Gwanghwamun (Korean: 광화문; Hanja: 光化門) is the main and south gate of the palace G...
2025-06-15 23:48:08,091 - INFO -    ✅ Row 14 enhanced successfully with keyword: Gwanghwamun Gate (20.6s)
2025-06-15 23:48:08,091 - INFO - 🔄 Processing row 15/378 (4.0% complete, ETA: 1.9h)
2025-06-15 23:48:08,092 - INFO -    📝 Korean term: 대형마트
2025-06-15 23:48:08,092 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '대형마트'...
2025-06-15 23:48:08,092 - INFO -      🧠 Starting LLM keyword generation for '대형마트'
2025-06-15 23:48:08,092 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:48:08,092 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:48:08,092 - INFO -      ⏳ Generating response...
2025-06-15 23:48:08,092 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:48:26,904 - INFO -      ⏱️  LLM inference completed in 18.81 seconds
2025-06-15 23:48:26,904 - INFO -      📤 Generated 2992 characters of response
2025-06-15 23:48:26,904 - INFO -      ✅ LLM attempt 1 generated: "E-Mart"  Category: Retail company Category: South Korean companies Category: So...
2025-06-15 23:48:26,905 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:48:26,905 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:48:26,905 - INFO -      🔍 Extracting keyword from response: "E-Mart"  Category: Retail company Category: South Korean co...
2025-06-15 23:48:26,905 - INFO -      📝 Raw keyword extracted: 'E-Mart'
2025-06-15 23:48:26,906 - INFO -      🎉 Successfully extracted valid keyword: 'E-Mart'
2025-06-15 23:48:26,906 - INFO -    📚 Step 3/3: Searching Wikipedia for 'E-Mart'...
2025-06-15 23:48:26,906 - INFO -      🔍 Searching Wikipedia for: 'E-Mart'
2025-06-15 23:48:26,906 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:48:28,261 - INFO -        ✅ Exact match found: 'Emart'
2025-06-15 23:48:28,261 - INFO -      ✅ Found page: 'Emart'
2025-06-15 23:48:28,261 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:48:28,897 - INFO -      📏 Extracted 176 characters of content
2025-06-15 23:48:28,897 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:48:28,897 - INFO -      🎉 Successfully retrieved knowledge from: Emart
2025-06-15 23:48:28,898 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Emart
2025-06-15 23:48:28,898 - INFO -    🎉 Row 14: Successfully enhanced! '대형마트' → 'E-Mart'
2025-06-15 23:48:28,898 - INFO -    📄 Knowledge preview: Emart Inc. (Korean: 주식회사 이마트) is the largest retailer in South Korea. The retail...
2025-06-15 23:48:28,898 - INFO -    ✅ Row 15 enhanced successfully with keyword: E-Mart (20.8s)
2025-06-15 23:48:28,899 - INFO - 💓 Heartbeat: 15 rows processed, avg 19.7s/row
2025-06-15 23:48:28,899 - INFO - 🔄 Processing row 16/378 (4.2% complete, ETA: 1.9h)
2025-06-15 23:48:28,899 - INFO -    📝 Korean term: 떡집
2025-06-15 23:48:28,899 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '떡집'...
2025-06-15 23:48:28,900 - INFO -      🧠 Starting LLM keyword generation for '떡집'
2025-06-15 23:48:28,900 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:48:28,900 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:48:28,900 - INFO -      ⏳ Generating response...
2025-06-15 23:48:28,900 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:48:47,622 - INFO -      ⏱️  LLM inference completed in 18.72 seconds
2025-06-15 23:48:47,622 - INFO -      📤 Generated 2380 characters of response
2025-06-15 23:48:47,622 - INFO -      ✅ LLM attempt 1 generated: "Tteokguk"

This Wikipedia page title is specific to the traditional Korean rice...
2025-06-15 23:48:47,623 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:48:47,623 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:48:47,623 - INFO -      🔍 Extracting keyword from response: "Tteokguk"

This Wikipedia page title is specific to the tra...
2025-06-15 23:48:47,623 - INFO -      📝 Raw keyword extracted: 'Tteok'
2025-06-15 23:48:47,623 - INFO -      🎉 Successfully extracted valid keyword: 'Tteok'
2025-06-15 23:48:47,623 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Tteok'...
2025-06-15 23:48:47,623 - INFO -      🔍 Searching Wikipedia for: 'Tteok'
2025-06-15 23:48:47,623 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:48:48,364 - INFO -        ✅ Exact match found: 'Tteok'
2025-06-15 23:48:48,364 - INFO -      ✅ Found page: 'Tteok'
2025-06-15 23:48:48,364 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:48:49,007 - INFO -      📏 Extracted 226 characters of content
2025-06-15 23:48:49,007 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:48:49,007 - INFO -      🎉 Successfully retrieved knowledge from: Tteok
2025-06-15 23:48:49,007 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Tteok
2025-06-15 23:48:49,008 - INFO -    🎉 Row 15: Successfully enhanced! '떡집' → 'Tteok'
2025-06-15 23:48:49,008 - INFO -    📄 Knowledge preview: Tteok (Korean: 떡) is a general term for Korean rice cakes. They are made with st...
2025-06-15 23:48:49,008 - INFO -    ✅ Row 16 enhanced successfully with keyword: Tteok (20.1s)
2025-06-15 23:48:49,009 - INFO - 🔄 Processing row 17/378 (4.5% complete, ETA: 1.9h)
2025-06-15 23:48:49,009 - INFO -    📝 Korean term: 고기집
2025-06-15 23:48:49,010 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '고기집'...
2025-06-15 23:48:49,010 - INFO -      🧠 Starting LLM keyword generation for '고기집'
2025-06-15 23:48:49,010 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:48:49,010 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:48:49,010 - INFO -      ⏳ Generating response...
2025-06-15 23:48:49,010 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:49:07,764 - INFO -      ⏱️  LLM inference completed in 18.75 seconds
2025-06-15 23:49:07,765 - INFO -      📤 Generated 2017 characters of response
2025-06-15 23:49:07,765 - INFO -      ✅ LLM attempt 1 generated: "Shin Ramyeon (Spicy Instant Noodle) Wikipedia page title" does not apply here. ...
2025-06-15 23:49:07,766 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:49:07,766 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:49:07,766 - INFO -      🔍 Extracting keyword from response: "Shin Ramyeon (Spicy Instant Noodle) Wikipedia page title" d...
2025-06-15 23:49:07,766 - INFO -      📝 Raw keyword extracted: '"Korean grilling etiquette" does not apply here'
2025-06-15 23:49:07,766 - WARNING -      ❌ Extracted keyword '"Korean grilling etiquette" does not apply here' failed validation
2025-06-15 23:49:07,766 - WARNING -    ❌ Row 16: Failed to extract valid keyword from: "Shin Ramyeon (Spicy Instant Noodle) Wikipedia pag...
2025-06-15 23:49:07,766 - INFO -    ❌ Row 17 enhancement failed (18.8s)
2025-06-15 23:49:07,767 - INFO - 🔄 Processing row 18/378 (4.8% complete, ETA: 1.9h)
2025-06-15 23:49:07,768 - INFO -    📝 Korean term: 찌개집
2025-06-15 23:49:07,768 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '찌개집'...
2025-06-15 23:49:07,768 - INFO -      🧠 Starting LLM keyword generation for '찌개집'
2025-06-15 23:49:07,768 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:49:07,768 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:49:07,768 - INFO -      ⏳ Generating response...
2025-06-15 23:49:07,768 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:49:26,401 - INFO -      ⏱️  LLM inference completed in 18.63 seconds
2025-06-15 23:49:26,401 - INFO -      📤 Generated 1426 characters of response
2025-06-15 23:49:26,401 - INFO -      ✅ LLM attempt 1 generated: "Jjigae" 

Explanation: Jjigae is a Korean term for a type of stew, which is the...
2025-06-15 23:49:26,401 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:49:26,402 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:49:26,402 - INFO -      🔍 Extracting keyword from response: "Jjigae" 

Explanation: Jjigae is a Korean term for a type o...
2025-06-15 23:49:26,402 - INFO -      📝 Raw keyword extracted: 'Jjigae'
2025-06-15 23:49:26,403 - INFO -      🎉 Successfully extracted valid keyword: 'Jjigae'
2025-06-15 23:49:26,403 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Jjigae'...
2025-06-15 23:49:26,403 - INFO -      🔍 Searching Wikipedia for: 'Jjigae'
2025-06-15 23:49:26,403 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:49:27,242 - INFO -        ✅ Exact match found: 'Jjigae'
2025-06-15 23:49:27,242 - INFO -      ✅ Found page: 'Jjigae'
2025-06-15 23:49:27,242 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:49:27,852 - INFO -      📏 Extracted 322 characters of content
2025-06-15 23:49:27,852 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:49:27,852 - INFO -      🎉 Successfully retrieved knowledge from: Jjigae
2025-06-15 23:49:27,852 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Jjigae
2025-06-15 23:49:27,853 - INFO -    🎉 Row 17: Successfully enhanced! '찌개집' → 'Jjigae'
2025-06-15 23:49:27,853 - INFO -    📄 Knowledge preview: Jjigae (Korean: 찌개; pronounced ) are Korean stews. There are many varieties; the...
2025-06-15 23:49:27,854 - INFO -    ✅ Row 18 enhanced successfully with keyword: Jjigae (20.1s)
2025-06-15 23:49:27,854 - INFO - 🔄 Processing row 19/378 (5.0% complete, ETA: 1.8h)
2025-06-15 23:49:27,855 - INFO -    📝 Korean term: 국밥집
2025-06-15 23:49:27,856 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '국밥집'...
2025-06-15 23:49:27,856 - INFO -      🧠 Starting LLM keyword generation for '국밥집'
2025-06-15 23:49:27,856 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:49:27,856 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:49:27,856 - INFO -      ⏳ Generating response...
2025-06-15 23:49:27,856 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:49:46,537 - INFO -      ⏱️  LLM inference completed in 18.68 seconds
2025-06-15 23:49:46,537 - INFO -      📤 Generated 2413 characters of response
2025-06-15 23:49:46,537 - INFO -      ✅ LLM attempt 1 generated: Jjimjilbang (Korean public bathhouse) and its role in Korean community life. 

J...
2025-06-15 23:49:46,538 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:49:46,538 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:49:46,538 - INFO -      🔍 Extracting keyword from response: Jjimjilbang (Korean public bathhouse) and its role in Korean...
2025-06-15 23:49:46,538 - INFO -      📝 Raw keyword extracted: 'Nureongi'
2025-06-15 23:49:46,538 - INFO -      🎉 Successfully extracted valid keyword: 'Nureongi'
2025-06-15 23:49:46,538 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Nureongi'...
2025-06-15 23:49:46,538 - INFO -      🔍 Searching Wikipedia for: 'Nureongi'
2025-06-15 23:49:46,538 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:49:47,132 - INFO - Enhancement interrupted by user
2025-06-15 23:49:47,132 - INFO - Cleaning up resources
2025-06-15 23:49:47,205 - INFO - LLM model resources cleaned up
2025-06-15 23:49:47,206 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:50:05,571 - INFO - 🚀 Initializing VQA Enhancement System
2025-06-15 23:50:05,571 - INFO - 📡 Step 1/3: Testing Wikipedia connection...
2025-06-15 23:50:06,466 - INFO - ✅ Wikipedia connection successful
2025-06-15 23:50:06,466 - INFO - 🧠 Step 2/3: Initializing LLM model (this may take a while)...
2025-06-15 23:50:07,486 - INFO - 🚀 Detected 8 GPU(s): [0, 1, 2, 3, 4, 5, 6, 7]
2025-06-15 23:50:07,488 - INFO - 🔥 Multi-GPU mode enabled with 8 GPUs
2025-06-15 23:50:07,488 - INFO - 🤖 Initializing meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:50:07,488 - INFO - 🔥 Using multi-GPU setup with 8 GPUs
2025-06-15 23:50:07,488 - INFO - 📝 Loading tokenizer...
2025-06-15 23:50:08,267 - INFO - 🔥 Loading model across 8 GPUs...
2025-06-15 23:50:08,321 - INFO -    GPU 0: 40GB allocated
2025-06-15 23:50:08,321 - INFO -    GPU 1: 40GB allocated
2025-06-15 23:50:08,321 - INFO -    GPU 2: 40GB allocated
2025-06-15 23:50:08,322 - INFO -    GPU 3: 40GB allocated
2025-06-15 23:50:08,322 - INFO -    GPU 4: 40GB allocated
2025-06-15 23:50:08,322 - INFO -    GPU 5: 40GB allocated
2025-06-15 23:50:08,322 - INFO -    GPU 6: 40GB allocated
2025-06-15 23:50:08,322 - INFO -    GPU 7: 40GB allocated
2025-06-15 23:50:15,890 - INFO - 🔧 Creating inference pipeline...
2025-06-15 23:50:15,891 - INFO - ✅ LLM model initialized successfully
2025-06-15 23:50:15,892 - INFO -    GPU 0: 1.4GB allocated, 1.4GB cached, 47.5GB total
2025-06-15 23:50:15,892 - INFO -    GPU 1: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,893 - INFO -    GPU 2: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,893 - INFO -    GPU 3: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,893 - INFO -    GPU 4: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,894 - INFO -    GPU 5: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,894 - INFO -    GPU 6: 2.0GB allocated, 2.0GB cached, 47.5GB total
2025-06-15 23:50:15,895 - INFO -    GPU 7: 1.4GB allocated, 1.4GB cached, 47.5GB total
2025-06-15 23:50:15,895 - INFO - ✅ LLM model loaded in 9.4 seconds
2025-06-15 23:50:15,895 - INFO - 🧪 Step 3/3: Testing LLM model...
2025-06-15 23:50:15,895 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:50:36,637 - INFO -      ⏱️  LLM inference completed in 20.74 seconds
2025-06-15 23:50:36,637 - INFO -      📤 Generated 2165 characters of response
2025-06-15 23:50:36,637 - INFO - 🎉 All components initialized successfully in 31.1 seconds
2025-06-15 23:50:36,638 - INFO - 📊 Model: meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:50:36,638 - INFO - 💻 Device: cuda
2025-06-15 23:50:36,638 - INFO - 🚀 CUDA devices available: 8
2025-06-15 23:50:36,638 - INFO - 🔥 Multi-GPU mode: Using 8 GPUs [0, 1, 2, 3, 4, 5, 6, 7]
2025-06-15 23:50:36,638 - INFO -    GPU_0: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_1: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_2: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_3: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_4: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_5: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_6: 47.5GB
2025-06-15 23:50:36,638 - INFO -    GPU_7: 47.5GB
2025-06-15 23:50:36,638 - INFO - ⚡ Expected speedup: ~8x faster inference
2025-06-15 23:50:36,638 - INFO - Loading dataset from: VQA.csv
2025-06-15 23:50:36,654 - INFO - Backup created: VQA_backup.csv
2025-06-15 23:50:36,656 - INFO - 📋 Processing rows 0 to 377 of 378
2025-06-15 23:50:36,656 - INFO - 🔄 Processing row 1/378 (0.3% complete)
2025-06-15 23:50:36,656 - INFO -    📝 Korean term: 제주 돌집
2025-06-15 23:50:36,657 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '제주 돌집'...
2025-06-15 23:50:36,657 - INFO -      🧠 Starting LLM keyword generation for '제주 돌집'
2025-06-15 23:50:36,657 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:50:36,657 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:50:36,657 - INFO -      ⏳ Generating response...
2025-06-15 23:50:36,657 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:50:55,808 - INFO -      ⏱️  LLM inference completed in 19.15 seconds
2025-06-15 23:50:55,808 - INFO -      📤 Generated 2520 characters of response
2025-06-15 23:50:55,808 - INFO -      ✅ LLM attempt 1 generated: Jeju Black House architecture

Explanation: Jeju Black House (Jeju doljip) is a ...
2025-06-15 23:50:55,808 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:50:55,808 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:50:55,809 - INFO -      🔍 Extracting keyword from response: Jeju Black House architecture

Explanation: Jeju Black House...
2025-06-15 23:50:55,809 - INFO -      📝 Raw keyword extracted: 's unique geology, which is characterized by volcanic rocks and rugged terrain. The island'
2025-06-15 23:50:55,809 - WARNING -      ❌ Extracted keyword 's unique geology, which is characterized by volcanic rocks and rugged terrain. The island' failed validation
2025-06-15 23:50:55,809 - WARNING -    ❌ Row 0: Failed to extract valid keyword from: Jeju Black House architecture

Explanation: Jeju B...
2025-06-15 23:50:55,809 - INFO -    ❌ Row 1 enhancement failed (19.2s)
2025-06-15 23:50:55,809 - INFO - 🔄 Processing row 2/378 (0.5% complete, ETA: 2.0h)
2025-06-15 23:50:55,810 - INFO -    📝 Korean term: 월정교
2025-06-15 23:50:55,810 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '월정교'...
2025-06-15 23:50:55,810 - INFO -      🧠 Starting LLM keyword generation for '월정교'
2025-06-15 23:50:55,811 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:50:55,811 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:50:55,811 - INFO -      ⏳ Generating response...
2025-06-15 23:50:55,811 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:51:14,887 - INFO -      ⏱️  LLM inference completed in 19.08 seconds
2025-06-15 23:51:14,888 - INFO -      📤 Generated 2002 characters of response
2025-06-15 23:51:14,888 - INFO -      ✅ LLM attempt 1 generated: Seokguram Grotto and Bulguksa Temple, Gyeongju, South Korea (UNESCO World Herita...
2025-06-15 23:51:14,889 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:51:14,889 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:51:14,889 - INFO -      🔍 Extracting keyword from response: Seokguram Grotto and Bulguksa Temple, Gyeongju, South Korea ...
2025-06-15 23:51:14,889 - INFO -      📝 Raw keyword extracted: 'Woljeonggyo Bridge'
2025-06-15 23:51:14,889 - INFO -      🎉 Successfully extracted valid keyword: 'Woljeonggyo Bridge'
2025-06-15 23:51:14,889 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Woljeonggyo Bridge'...
2025-06-15 23:51:14,889 - INFO -      🔍 Searching Wikipedia for: 'Woljeonggyo Bridge'
2025-06-15 23:51:14,889 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:51:16,202 - INFO -        ✅ Exact match found: 'Woljeonggyo'
2025-06-15 23:51:16,202 - INFO -      ✅ Found page: 'Woljeonggyo'
2025-06-15 23:51:16,202 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:51:16,873 - INFO -      📏 Extracted 113 characters of content
2025-06-15 23:51:16,873 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:51:16,873 - INFO -      🎉 Successfully retrieved knowledge from: Woljeonggyo
2025-06-15 23:51:16,874 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Woljeonggyo
2025-06-15 23:51:16,874 - INFO -    🎉 Row 1: Successfully enhanced! '월정교' → 'Woljeonggyo Bridge'
2025-06-15 23:51:16,874 - INFO -    📄 Knowledge preview: Woljeonggyo (Korean: 월정교; Hanja: 月精橋) is a covered bridge in Gyeongju, South Kor...
2025-06-15 23:51:16,874 - INFO -    ✅ Row 2 enhanced successfully with keyword: Woljeonggyo Bridge (21.1s)
2025-06-15 23:51:16,875 - INFO - 🔄 Processing row 3/378 (0.8% complete, ETA: 2.1h)
2025-06-15 23:51:16,875 - INFO -    📝 Korean term: 운현궁
2025-06-15 23:51:16,876 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '운현궁'...
2025-06-15 23:51:16,876 - INFO -      🧠 Starting LLM keyword generation for '운현궁'
2025-06-15 23:51:16,876 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:51:16,876 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:51:16,876 - INFO -      ⏳ Generating response...
2025-06-15 23:51:16,876 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:51:36,016 - INFO -      ⏱️  LLM inference completed in 19.14 seconds
2025-06-15 23:51:36,016 - INFO -      📤 Generated 2610 characters of response
2025-06-15 23:51:36,016 - INFO -      ✅ LLM attempt 1 generated: Gyeongbokgung Palace

Title: Unhyeon Palace

Title: Joseon Dynasty

Title: Chang...
2025-06-15 23:51:36,016 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:51:36,017 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:51:36,017 - INFO -      🔍 Extracting keyword from response: Gyeongbokgung Palace

Title: Unhyeon Palace

Title: Joseon D...
2025-06-15 23:51:36,017 - INFO -      📝 Raw keyword extracted: 'Unhyeon Palace'
2025-06-15 23:51:36,018 - INFO -      🎉 Successfully extracted valid keyword: 'Unhyeon Palace'
2025-06-15 23:51:36,018 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Unhyeon Palace'...
2025-06-15 23:51:36,018 - INFO -      🔍 Searching Wikipedia for: 'Unhyeon Palace'
2025-06-15 23:51:36,018 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:51:37,389 - INFO -        ✅ Exact match found: 'Unhyeongung'
2025-06-15 23:51:37,389 - INFO -      ✅ Found page: 'Unhyeongung'
2025-06-15 23:51:37,389 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:51:37,994 - INFO -      📏 Extracted 401 characters of content
2025-06-15 23:51:37,994 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:51:37,995 - INFO -      🎉 Successfully retrieved knowledge from: Unhyeongung
2025-06-15 23:51:37,995 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Unhyeongung
2025-06-15 23:51:37,996 - INFO -    🎉 Row 2: Successfully enhanced! '운현궁' → 'Unhyeon Palace'
2025-06-15 23:51:37,996 - INFO -    📄 Knowledge preview: Unhyeongung (Korean: 운현궁), also known as Unhyeongung Royal Residence, is a forme...
2025-06-15 23:51:37,996 - INFO -    ✅ Row 3 enhanced successfully with keyword: Unhyeon Palace (21.1s)
2025-06-15 23:51:37,996 - INFO - 🔄 Processing row 4/378 (1.1% complete, ETA: 2.1h)
2025-06-15 23:51:37,997 - INFO -    📝 Korean term: 명동
2025-06-15 23:51:37,998 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '명동'...
2025-06-15 23:51:37,998 - INFO -      🧠 Starting LLM keyword generation for '명동'
2025-06-15 23:51:37,998 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:51:37,998 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:51:37,998 - INFO -      ⏳ Generating response...
2025-06-15 23:51:37,998 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:51:57,207 - INFO -      ⏱️  LLM inference completed in 19.21 seconds
2025-06-15 23:51:57,207 - INFO -      📤 Generated 2441 characters of response
2025-06-15 23:51:57,207 - INFO -      ✅ LLM attempt 1 generated: Myeong-dong shopping district

Note: Myeong-dong is a shopping district in Seoul...
2025-06-15 23:51:57,208 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:51:57,208 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:51:57,208 - INFO -      🔍 Extracting keyword from response: Myeong-dong shopping district

Note: Myeong-dong is a shoppi...
2025-06-15 23:51:57,208 - INFO -      📝 Raw keyword extracted: 'Myeong-dong shopping district'
2025-06-15 23:51:57,208 - INFO -      🎉 Successfully extracted valid keyword: 'Myeong-dong shopping district'
2025-06-15 23:51:57,208 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Myeong-dong shopping district'...
2025-06-15 23:51:57,208 - INFO -      🔍 Searching Wikipedia for: 'Myeong-dong shopping district'
2025-06-15 23:51:57,208 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:51:59,321 - INFO -        ✅ Auto-suggest found: 'Myeong-dong'
2025-06-15 23:51:59,321 - INFO -      ✅ Found page: 'Myeong-dong'
2025-06-15 23:51:59,321 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:51:59,984 - INFO -      📏 Extracted 259 characters of content
2025-06-15 23:51:59,985 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:51:59,986 - INFO -      🎉 Successfully retrieved knowledge from: Myeong-dong
2025-06-15 23:51:59,986 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Myeong-dong
2025-06-15 23:51:59,986 - INFO -    🎉 Row 3: Successfully enhanced! '명동' → 'Myeong-dong shopping district'
2025-06-15 23:51:59,986 - INFO -    📄 Knowledge preview: Myeong-dong (Korean: 명동; lit. 'bright neighborhood') is a dong (neighborhood) in...
2025-06-15 23:51:59,988 - INFO -    ✅ Row 4 enhanced successfully with keyword: Myeong-dong shopping district (22.0s)
2025-06-15 23:51:59,988 - INFO - 🔄 Processing row 5/378 (1.3% complete, ETA: 2.2h)
2025-06-15 23:51:59,989 - INFO -    📝 Korean term: 남산타워
2025-06-15 23:51:59,989 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '남산타워'...
2025-06-15 23:51:59,989 - INFO -      🧠 Starting LLM keyword generation for '남산타워'
2025-06-15 23:51:59,989 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:51:59,989 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:51:59,989 - INFO -      ⏳ Generating response...
2025-06-15 23:51:59,989 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:52:19,119 - INFO -      ⏱️  LLM inference completed in 19.13 seconds
2025-06-15 23:52:19,119 - INFO -      📤 Generated 3318 characters of response
2025-06-15 23:52:19,119 - INFO -      ✅ LLM attempt 1 generated: Namsan Tower Wikipedia page title: "N Seoul Tower" Category: Landmarks in South ...
2025-06-15 23:52:19,119 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:52:19,120 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:52:19,120 - INFO -      🔍 Extracting keyword from response: Namsan Tower Wikipedia page title: "N Seoul Tower" Category:...
2025-06-15 23:52:19,120 - INFO -      📝 Raw keyword extracted: '"N Seoul Tower" Category: Landmarks in South Korea Category: Towers in Seoul Category: Tourist attractions in Seoul Category: Observation towers in South Korea Category: Observation towers in Seoul Category: Communications towers in South Korea Category: Communications towers in Seoul Category: Korean architecture Category: South Korean architecture Category: Romance symbols in South Korea Category: Romance symbols in Seoul Category: Love locks Category: Couples' attractions in Seoul Category: Couples' attractions in South Korea Category: Honeymoon destinations in South Korea Category: Honeymoon destinations in Seoul Category: Panoramic views of Seoul Category: Cityscape views of Seoul Category: Views of the Han River Category: Views of the Seoul skyline Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South'
2025-06-15 23:52:19,120 - WARNING -      ❌ Extracted keyword '"N Seoul Tower" Category: Landmarks in South Korea Category: Towers in Seoul Category: Tourist attractions in Seoul Category: Observation towers in South Korea Category: Observation towers in Seoul Category: Communications towers in South Korea Category: Communications towers in Seoul Category: Korean architecture Category: South Korean architecture Category: Romance symbols in South Korea Category: Romance symbols in Seoul Category: Love locks Category: Couples' attractions in Seoul Category: Couples' attractions in South Korea Category: Honeymoon destinations in South Korea Category: Honeymoon destinations in Seoul Category: Panoramic views of Seoul Category: Cityscape views of Seoul Category: Views of the Han River Category: Views of the Seoul skyline Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South Korean romance Category: South Korean culture Category: South Korean symbols Category: South Korean architecture Category: South Korean landmarks Category: South Korean tourist attractions Category: South Korean communication infrastructure Category: South Korean observation towers Category: South Korean towers Category: South' failed validation
2025-06-15 23:52:19,120 - WARNING -    ❌ Row 4: Failed to extract valid keyword from: Namsan Tower Wikipedia page title: "N Seoul Tower"...
2025-06-15 23:52:19,120 - INFO -    ❌ Row 5 enhancement failed (19.1s)
2025-06-15 23:52:19,120 - INFO - 💓 Heartbeat: 5 rows processed, avg 20.5s/row
2025-06-15 23:52:19,120 - INFO - 🔄 Processing row 6/378 (1.6% complete, ETA: 2.1h)
2025-06-15 23:52:19,121 - INFO -    📝 Korean term: 신라대종
2025-06-15 23:52:19,121 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '신라대종'...
2025-06-15 23:52:19,121 - INFO -      🧠 Starting LLM keyword generation for '신라대종'
2025-06-15 23:52:19,122 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:52:19,122 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:52:19,122 - INFO -      ⏳ Generating response...
2025-06-15 23:52:19,122 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:52:38,215 - INFO -      ⏱️  LLM inference completed in 19.09 seconds
2025-06-15 23:52:38,215 - INFO -      📤 Generated 2411 characters of response
2025-06-15 23:52:38,215 - INFO -      ✅ LLM attempt 1 generated: Gyeongcheonsa Temple's Seokguram Grotto Bronze Bell (Seokguram Grotto Bell) (Kor...
2025-06-15 23:52:38,216 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:52:38,216 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:52:38,216 - INFO -      🔍 Extracting keyword from response: Gyeongcheonsa Temple's Seokguram Grotto Bronze Bell (Seokgur...
2025-06-15 23:52:38,216 - INFO -      📝 Raw keyword extracted: 's Seokguram Grotto Bronze Bell   is a bronze bell located in Gyeongju, North Gyeongsang Province, South Korea. The bell is believed to have been cast during the Silla period, and its inscriptions and legendary origin reflect the spiritual beliefs of that era. It is a significant artifact that provides insight into the religious and cultural practices of the Silla people. The bell is also known for its large size and intricate design, making it a notable example of Korean metalwork from the period. The Seokguram Grotto, where the bell is housed, is a UNESCO World Heritage site and a popular tourist destination. The bell is also a symbol of Korean cultural heritage and is considered a national treasure. The Seokguram Grotto Bronze Bell is a valuable artifact that provides a glimpse into the spiritual and cultural practices of the Silla people during the Silla period. The bell is a significant example of Korean metalwork and is a testament to the skill and craftsmanship of the artisans of the time. The bell'
2025-06-15 23:52:38,216 - WARNING -      ❌ Extracted keyword 's Seokguram Grotto Bronze Bell   is a bronze bell located in Gyeongju, North Gyeongsang Province, South Korea. The bell is believed to have been cast during the Silla period, and its inscriptions and legendary origin reflect the spiritual beliefs of that era. It is a significant artifact that provides insight into the religious and cultural practices of the Silla people. The bell is also known for its large size and intricate design, making it a notable example of Korean metalwork from the period. The Seokguram Grotto, where the bell is housed, is a UNESCO World Heritage site and a popular tourist destination. The bell is also a symbol of Korean cultural heritage and is considered a national treasure. The Seokguram Grotto Bronze Bell is a valuable artifact that provides a glimpse into the spiritual and cultural practices of the Silla people during the Silla period. The bell is a significant example of Korean metalwork and is a testament to the skill and craftsmanship of the artisans of the time. The bell' failed validation
2025-06-15 23:52:38,216 - WARNING -    ❌ Row 5: Failed to extract valid keyword from: Gyeongcheonsa Temple's Seokguram Grotto Bronze Bel...
2025-06-15 23:52:38,216 - INFO -    ❌ Row 6 enhancement failed (19.1s)
2025-06-15 23:52:38,216 - INFO - 🔄 Processing row 7/378 (1.9% complete, ETA: 2.1h)
2025-06-15 23:52:38,217 - INFO -    📝 Korean term: 고려대학교
2025-06-15 23:52:38,217 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '고려대학교'...
2025-06-15 23:52:38,217 - INFO -      🧠 Starting LLM keyword generation for '고려대학교'
2025-06-15 23:52:38,217 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:52:38,217 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:52:38,218 - INFO -      ⏳ Generating response...
2025-06-15 23:52:38,218 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:52:57,322 - INFO -      ⏱️  LLM inference completed in 19.10 seconds
2025-06-15 23:52:57,322 - INFO -      📤 Generated 2926 characters of response
2025-06-15 23:52:57,322 - INFO -      ✅ LLM attempt 1 generated: Sogang University campus architecture

Note: Sogang University is a private rese...
2025-06-15 23:52:57,323 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:52:57,323 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:52:57,323 - INFO -      🔍 Extracting keyword from response: Sogang University campus architecture

Note: Sogang Universi...
2025-06-15 23:52:57,323 - INFO -      📝 Raw keyword extracted: 's heritage as a Jesuit institution, which was established in 1960. The Gothic architectural style is a reflection of the university'
2025-06-15 23:52:57,323 - WARNING -      ❌ Extracted keyword 's heritage as a Jesuit institution, which was established in 1960. The Gothic architectural style is a reflection of the university' failed validation
2025-06-15 23:52:57,324 - WARNING -    ❌ Row 6: Failed to extract valid keyword from: Sogang University campus architecture

Note: Sogan...
2025-06-15 23:52:57,324 - INFO -    ❌ Row 7 enhancement failed (19.1s)
2025-06-15 23:52:57,324 - INFO - 🔄 Processing row 8/378 (2.1% complete, ETA: 2.1h)
2025-06-15 23:52:57,325 - INFO -    📝 Korean term: 한강다리
2025-06-15 23:52:57,325 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '한강다리'...
2025-06-15 23:52:57,325 - INFO -      🧠 Starting LLM keyword generation for '한강다리'
2025-06-15 23:52:57,325 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:52:57,325 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:52:57,325 - INFO -      ⏳ Generating response...
2025-06-15 23:52:57,325 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:53:16,395 - INFO -      ⏱️  LLM inference completed in 19.07 seconds
2025-06-15 23:53:16,395 - INFO -      📤 Generated 2733 characters of response
2025-06-15 23:53:16,395 - INFO -      ✅ LLM attempt 1 generated: Han River Bridges in Seoul

(Not actually an English Wikipedia page title, but t...
2025-06-15 23:53:16,395 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:53:16,396 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:53:16,396 - INFO -      🔍 Extracting keyword from response: Han River Bridges in Seoul

(Not actually an English Wikiped...
2025-06-15 23:53:16,397 - INFO -      📝 Raw keyword extracted: 'Han River Bridges in Seoul'
2025-06-15 23:53:16,397 - WARNING -      ❌ Extracted keyword 'Han River Bridges in Seoul' failed validation
2025-06-15 23:53:16,397 - WARNING -    ❌ Row 7: Failed to extract valid keyword from: Han River Bridges in Seoul

(Not actually an Engli...
2025-06-15 23:53:16,397 - INFO -    ❌ Row 8 enhancement failed (19.1s)
2025-06-15 23:53:16,397 - INFO - 🔄 Processing row 9/378 (2.4% complete, ETA: 2.0h)
2025-06-15 23:53:16,398 - INFO -    📝 Korean term: DDP
2025-06-15 23:53:16,398 - INFO -    🤖 Step 1/3: Generating keywords using LLM for 'DDP'...
2025-06-15 23:53:16,398 - INFO -      🧠 Starting LLM keyword generation for 'DDP'
2025-06-15 23:53:16,398 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:53:16,398 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:53:16,398 - INFO -      ⏳ Generating response...
2025-06-15 23:53:16,398 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:53:35,469 - INFO -      ⏱️  LLM inference completed in 19.07 seconds
2025-06-15 23:53:35,470 - INFO -      📤 Generated 1151 characters of response
2025-06-15 23:53:35,470 - INFO -      ✅ LLM attempt 1 generated: Dongdaemun Design Plaza (DDP) - Wikipedia page title. 

[Note: Dongdaemun Design...
2025-06-15 23:53:35,470 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:53:35,470 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:53:35,471 - INFO -      🔍 Extracting keyword from response: Dongdaemun Design Plaza (DDP) - Wikipedia page title. 

[Not...
2025-06-15 23:53:35,471 - INFO -      📝 Raw keyword extracted: 'Note'
2025-06-15 23:53:35,472 - INFO -      🎉 Successfully extracted valid keyword: 'Note'
2025-06-15 23:53:35,472 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Note'...
2025-06-15 23:53:35,472 - INFO -      🔍 Searching Wikipedia for: 'Note'
2025-06-15 23:53:35,472 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:53:37,389 - INFO -        🔀 Disambiguation found for 'Note', trying: Musical note
2025-06-15 23:53:37,987 - INFO -        ✅ Disambiguation resolved: 'Musical note'
2025-06-15 23:53:37,988 - INFO -      ✅ Found page: 'Musical note'
2025-06-15 23:53:37,988 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:53:38,994 - INFO -      📏 Extracted 264 characters of content
2025-06-15 23:53:38,994 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:53:38,994 - WARNING -      ❌ Content not relevant to Korean culture: Musical note
2025-06-15 23:53:38,996 - WARNING -    ❌ Row 8: Failed to get Wikipedia content for: Note
2025-06-15 23:53:38,996 - INFO -    ❌ Row 9 enhancement failed (22.6s)
2025-06-15 23:53:38,996 - INFO - 🔄 Processing row 10/378 (2.6% complete, ETA: 2.1h)
2025-06-15 23:53:38,997 - INFO -    📝 Korean term: 탑골공원
2025-06-15 23:53:38,997 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '탑골공원'...
2025-06-15 23:53:38,997 - INFO -      🧠 Starting LLM keyword generation for '탑골공원'
2025-06-15 23:53:38,998 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:53:38,998 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:53:38,998 - INFO -      ⏳ Generating response...
2025-06-15 23:53:38,998 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:53:48,978 - INFO -      ⏱️  LLM inference completed in 9.98 seconds
2025-06-15 23:53:48,978 - INFO -      📤 Generated 1347 characters of response
2025-06-15 23:53:48,978 - INFO -      ✅ LLM attempt 1 generated: Tapgol Park Pagoda and Independence Movement

Note: I have reviewed the prompt a...
2025-06-15 23:53:48,979 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:53:48,979 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:53:48,979 - INFO -      🔍 Extracting keyword from response: Tapgol Park Pagoda and Independence Movement

Note: I have r...
2025-06-15 23:53:48,979 - INFO -      📝 Raw keyword extracted: '탑골공원'
2025-06-15 23:53:48,980 - WARNING -      ❌ Extracted keyword '탑골공원' failed validation
2025-06-15 23:53:48,980 - WARNING -    ❌ Row 9: Failed to extract valid keyword from: Tapgol Park Pagoda and Independence Movement

Note...
2025-06-15 23:53:48,980 - INFO -    ❌ Row 10 enhancement failed (10.0s)
2025-06-15 23:53:48,980 - INFO - 💓 Heartbeat: 10 rows processed, avg 19.2s/row
2025-06-15 23:53:48,980 - INFO - 💾 Saving progress at row 10...
2025-06-15 23:53:48,988 - INFO -    📊 Current success rate: 30.0% (3/10 rows)
2025-06-15 23:53:48,988 - INFO -    ⏱️  Total elapsed time: 3.2 minutes
2025-06-15 23:53:48,989 - INFO - 🔄 Processing row 11/378 (2.9% complete, ETA: 2.0h)
2025-06-15 23:53:48,989 - INFO -    📝 Korean term: PC방
2025-06-15 23:53:48,989 - INFO -    🤖 Step 1/3: Generating keywords using LLM for 'PC방'...
2025-06-15 23:53:48,989 - INFO -      🧠 Starting LLM keyword generation for 'PC방'
2025-06-15 23:53:48,989 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:53:48,989 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:53:48,989 - INFO -      ⏳ Generating response...
2025-06-15 23:53:48,990 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:54:08,132 - INFO -      ⏱️  LLM inference completed in 19.14 seconds
2025-06-15 23:54:08,132 - INFO -      📤 Generated 1295 characters of response
2025-06-15 23:54:08,132 - INFO -      ✅ LLM attempt 1 generated: "PC bang"  Category: Computer gaming venues, Korean culture, social habits, yout...
2025-06-15 23:54:08,133 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:54:08,133 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:54:08,133 - INFO -      🔍 Extracting keyword from response: "PC bang"  Category: Computer gaming venues, Korean culture,...
2025-06-15 23:54:08,133 - INFO -      📝 Raw keyword extracted: 'PC bang'
2025-06-15 23:54:08,133 - WARNING -      ❌ Extracted keyword 'PC bang' failed validation
2025-06-15 23:54:08,133 - WARNING -    ❌ Row 10: Failed to extract valid keyword from: "PC bang"  Category: Computer gaming venues, Korea...
2025-06-15 23:54:08,134 - INFO -    ❌ Row 11 enhancement failed (19.1s)
2025-06-15 23:54:08,134 - INFO - 🔄 Processing row 12/378 (3.2% complete, ETA: 2.0h)
2025-06-15 23:54:08,135 - INFO -    📝 Korean term: 분식집
2025-06-15 23:54:08,135 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '분식집'...
2025-06-15 23:54:08,135 - INFO -      🧠 Starting LLM keyword generation for '분식집'
2025-06-15 23:54:08,135 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:54:08,135 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:54:08,135 - INFO -      ⏳ Generating response...
2025-06-15 23:54:08,135 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:54:27,290 - INFO -      ⏱️  LLM inference completed in 19.15 seconds
2025-06-15 23:54:27,290 - INFO -      📤 Generated 1655 characters of response
2025-06-15 23:54:27,290 - INFO -      ✅ LLM attempt 1 generated: "Tteokbokki" or "Tteokbokki culture" is not a specific term. 

Title: "Tteokbokk...
2025-06-15 23:54:27,291 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:54:27,291 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:54:27,291 - INFO -      🔍 Extracting keyword from response: "Tteokbokki" or "Tteokbokki culture" is not a specific term....
2025-06-15 23:54:27,291 - INFO -      📝 Raw keyword extracted: '"Tteokbokki culture" is still not a specific term'
2025-06-15 23:54:27,292 - WARNING -      ❌ Extracted keyword '"Tteokbokki culture" is still not a specific term' failed validation
2025-06-15 23:54:27,292 - WARNING -    ❌ Row 11: Failed to extract valid keyword from: "Tteokbokki" or "Tteokbokki culture" is not a spec...
2025-06-15 23:54:27,292 - INFO -    ❌ Row 12 enhancement failed (19.2s)
2025-06-15 23:54:27,292 - INFO - 🔄 Processing row 13/378 (3.4% complete, ETA: 1.9h)
2025-06-15 23:54:27,293 - INFO -    📝 Korean term: 빵집
2025-06-15 23:54:27,293 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '빵집'...
2025-06-15 23:54:27,293 - INFO -      🧠 Starting LLM keyword generation for '빵집'
2025-06-15 23:54:27,293 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:54:27,293 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:54:27,293 - INFO -      ⏳ Generating response...
2025-06-15 23:54:27,293 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:54:46,468 - INFO -      ⏱️  LLM inference completed in 19.17 seconds
2025-06-15 23:54:46,469 - INFO -      📤 Generated 2683 characters of response
2025-06-15 23:54:46,469 - INFO -      ✅ LLM attempt 1 generated: European_Baking_Techniques_in_Korean_Pastry_Shop

https://en.wikipedia.org/wiki/...
2025-06-15 23:54:46,469 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:54:46,469 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:54:46,469 - INFO -      🔍 Extracting keyword from response: European_Baking_Techniques_in_Korean_Pastry_Shop

https://en...
2025-06-15 23:54:46,470 - INFO -      📝 Raw keyword extracted: '빵집'
2025-06-15 23:54:46,470 - WARNING -      ❌ Extracted keyword '빵집' failed validation
2025-06-15 23:54:46,470 - WARNING -    ❌ Row 12: Failed to extract valid keyword from: European_Baking_Techniques_in_Korean_Pastry_Shop

...
2025-06-15 23:54:46,470 - INFO -    ❌ Row 13 enhancement failed (19.2s)
2025-06-15 23:54:46,470 - INFO - 🔄 Processing row 14/378 (3.7% complete, ETA: 1.9h)
2025-06-15 23:54:46,471 - INFO -    📝 Korean term: 광화문
2025-06-15 23:54:46,471 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '광화문'...
2025-06-15 23:54:46,471 - INFO -      🧠 Starting LLM keyword generation for '광화문'
2025-06-15 23:54:46,471 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:54:46,471 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:54:46,471 - INFO -      ⏳ Generating response...
2025-06-15 23:54:46,471 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:54:48,147 - INFO - Enhancement interrupted by user
2025-06-15 23:54:48,148 - INFO - Cleaning up resources
2025-06-15 23:54:48,249 - INFO - LLM model resources cleaned up
2025-06-15 23:54:48,250 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
