2025-06-15 23:03:01,613 - INFO - Initializing VQA Enhancement System
2025-06-15 23:03:02,439 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:03:06,061 - INFO - LLM model initialized successfully
2025-06-15 23:08:54,946 - INFO - Enhancement interrupted by user
2025-06-15 23:08:54,946 - INFO - Cleaning up resources
2025-06-15 23:08:57,056 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:09:07,378 - INFO - Initializing VQA Enhancement System
2025-06-15 23:09:08,314 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:09:12,244 - INFO - LLM model initialized successfully
2025-06-15 23:09:14,408 - INFO - Enhancement interrupted by user
2025-06-15 23:09:14,409 - INFO - Cleaning up resources
2025-06-15 23:09:16,531 - ERROR - Error during cleanup: 'LLMHandler' object has no attribute 'model'
2025-06-15 23:19:10,993 - INFO - 🚀 Initializing VQA Enhancement System
2025-06-15 23:19:10,993 - INFO - 📡 Step 1/3: Testing Wikipedia connection...
2025-06-15 23:19:11,812 - INFO - ✅ Wikipedia connection successful
2025-06-15 23:19:11,813 - INFO - 🧠 Step 2/3: Initializing LLM model (this may take a while)...
2025-06-15 23:19:11,813 - INFO - Initializing meta-llama/Llama-3.1-8B-Instruct on cpu
2025-06-15 23:19:15,466 - INFO - LLM model initialized successfully
2025-06-15 23:19:15,466 - INFO - ✅ LLM model loaded in 3.7 seconds
2025-06-15 23:19:15,466 - INFO - 🧪 Step 3/3: Testing LLM model...
2025-06-15 23:19:15,467 - INFO -      🚀 Starting LLM inference...
2025-06-15 23:31:49,380 - INFO -      ⏱️  LLM inference completed in 753.91 seconds
2025-06-15 23:31:49,381 - INFO -      📤 Generated 2526 characters of response
2025-06-15 23:31:49,381 - INFO - 🎉 All components initialized successfully in 758.4 seconds
2025-06-15 23:31:51,649 - INFO - 📊 Model: meta-llama/Llama-3.1-8B-Instruct
2025-06-15 23:31:51,650 - INFO - 💻 Device: cpu
2025-06-15 23:31:51,651 - INFO - 🚀 CUDA devices available: 8
2025-06-15 23:31:51,651 - INFO - Loading dataset from: VQA.csv
2025-06-15 23:31:51,674 - INFO - Backup created: VQA_backup.csv
2025-06-15 23:31:51,676 - INFO - 📋 Processing rows 0 to 377 of 378
2025-06-15 23:31:51,676 - INFO - 🔄 Processing row 1/378 (0.3% complete)
2025-06-15 23:31:51,676 - INFO -    📝 Korean term: 제주 돌집
2025-06-15 23:31:51,677 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '제주 돌집'...
2025-06-15 23:31:51,677 - INFO -      🧠 Starting LLM keyword generation for '제주 돌집'
2025-06-15 23:31:51,677 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:31:51,677 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:31:51,677 - INFO -      ⏳ Generating response...
2025-06-15 23:31:51,677 - INFO -      🚀 Starting LLM inference...
