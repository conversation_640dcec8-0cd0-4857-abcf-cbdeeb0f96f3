"""
Quick test of enhanced Wikipedia search for key failing cases.
"""

import logging
from wikipedia_handler import WikipediaHandler

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def quick_test():
    """Test key cases that were failing before."""
    
    handler = WikipediaHandler()
    
    # Key test cases that were mentioned as failing
    test_cases = [
        "N Seoul Tower",
        "Seoul Tower", 
        "Namsan Tower",
        "DDP",
        "Dongdaemun Design Plaza",
        "Gyeongbokgung",
        "Bulguksa",
        "Cheomseongdae",
        "Jeju Stone House",
        "Woljeonggyo"
    ]
    
    logger.info("🧪 Quick Test of Enhanced Wikipedia Search")
    logger.info("=" * 50)
    
    successful = 0
    failed = 0
    
    for keyword in test_cases:
        logger.info(f"\n🔍 Testing: '{keyword}'")
        
        try:
            knowledge_point, source_url = handler.get_knowledge_point(keyword)
            
            if knowledge_point and source_url:
                logger.info(f"✅ SUCCESS: {keyword}")
                logger.info(f"   📄 {knowledge_point[:60]}...")
                successful += 1
            else:
                logger.warning(f"❌ FAILED: {keyword}")
                failed += 1
                
        except Exception as e:
            logger.error(f"💥 ERROR: {keyword} - {str(e)}")
            failed += 1
    
    total = successful + failed
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    logger.info(f"\n📊 Results: {successful}/{total} successful ({success_rate:.1f}%)")
    
    if success_rate > 80:
        logger.info("🎉 Excellent improvement!")
    elif success_rate > 60:
        logger.info("👍 Good improvement!")
    else:
        logger.info("⚠️  Needs more work")

if __name__ == "__main__":
    quick_test()
