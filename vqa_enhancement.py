"""
VQA Dataset Enhancement System
Main script that orchestrates the entire process of adding Wikipedia-sourced knowledge
to Korean cultural questions using LLM-based keyword generation.
"""

import pandas as pd
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
import argparse

from config import LOG_LEVEL, LOG_FORMAT, LOG_FILE, OUTPUT_COLUMNS, BACKUP_SUFFIX
from llm_handler import LLMHandler
from wikipedia_handler import <PERSON><PERSON><PERSON><PERSON>
from keyword_extractor import KeywordExtractor

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[logging.FileHandler(LOG_FILE), logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


class VQAEnhancer:
    """Main class for VQA dataset enhancement."""

    def __init__(self):
        self.llm_handler = <PERSON><PERSON>Handler()
        self.wikipedia_handler = <PERSON><PERSON>andler()
        self.keyword_extractor = KeywordExtractor()
        self.stats = {
            "total_rows": 0,
            "successful_enhancements": 0,
            "automated_success": 0,
            "manual_wikipedia_success": 0,
            "manual_non_wikipedia_kept": 0,
            "llm_generated_success": 0,
            "failed_llm": 0,
            "failed_keyword_extraction": 0,
            "failed_wikipedia": 0,
            "generic_keywords_rejected": 0,
        }

    def initialize(self) -> bool:
        """Initialize all components."""
        import time

        logger.info("🚀 Initializing VQA Enhancement System")
        start_time = time.time()

        # Test Wikipedia connection
        logger.info("📡 Step 1/3: Testing Wikipedia connection...")
        if not self.wikipedia_handler.test_connection():
            logger.error("❌ Wikipedia connection test failed")
            return False
        logger.info("✅ Wikipedia connection successful")

        # Initialize LLM model
        logger.info("🧠 Step 2/3: Initializing LLM model (this may take a while)...")
        llm_start = time.time()
        if not self.llm_handler.initialize_model():
            logger.warning(
                "⚠️  LLM model initialization failed - will skip LLM-based enhancement"
            )
            return False
        llm_time = time.time() - llm_start
        logger.info(f"✅ LLM model loaded in {llm_time:.1f} seconds")

        # Test LLM model
        logger.info("🧪 Step 3/3: Testing LLM model...")
        if not self.llm_handler.test_model():
            logger.error("❌ LLM model test failed")
            return False

        total_time = time.time() - start_time
        logger.info(
            f"🎉 All components initialized successfully in {total_time:.1f} seconds"
        )

        # Print model info
        model_info = self.llm_handler.get_model_info()
        logger.info(f"📊 Model: {model_info['model_name']}")
        logger.info(f"💻 Device: {model_info['device']}")

        if model_info["cuda_available"]:
            logger.info(f"🚀 CUDA devices available: {model_info['device_count']}")
            if model_info["use_multi_gpu"]:
                logger.info(
                    f"🔥 Multi-GPU mode: Using {model_info['num_gpus']} GPUs {model_info['gpu_devices']}"
                )
                for gpu_id, memory in model_info.get("gpu_memory", {}).items():
                    logger.info(f"   {gpu_id.upper()}: {memory}")
                logger.info(
                    f"⚡ Expected speedup: ~{model_info['num_gpus']}x faster inference"
                )
            else:
                logger.info(
                    f"📱 Single GPU mode: GPU {model_info['gpu_devices'][0] if model_info['gpu_devices'] else 0}"
                )
        else:
            logger.info("💻 CPU mode - consider using GPU for faster processing")

        return True

    def enhance_dataset(
        self,
        input_file: str,
        output_file: str,
        start_row: int = 0,
        max_rows: Optional[int] = None,
    ) -> bool:
        """
        Enhance the VQA dataset with Wikipedia knowledge.

        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file
            start_row: Row to start processing from (for resuming)
            max_rows: Maximum number of rows to process (None for all)
        """
        try:
            # Load dataset
            logger.info(f"Loading dataset from: {input_file}")
            df = pd.read_csv(input_file)

            # Create backup
            backup_file = input_file.replace(".csv", f"{BACKUP_SUFFIX}.csv")
            df.to_csv(backup_file, index=False)
            logger.info(f"Backup created: {backup_file}")

            # Initialize new columns if they don't exist
            for col in OUTPUT_COLUMNS:
                if col not in df.columns:
                    df[col] = ""

            # Determine processing range
            total_rows = len(df)
            end_row = min(start_row + max_rows, total_rows) if max_rows else total_rows

            logger.info(
                f"📋 Processing rows {start_row} to {end_row-1} of {total_rows}"
            )
            self.stats["total_rows"] = end_row - start_row

            # Track timing for ETA calculation
            import time

            processing_start_time = time.time()
            row_times = []

            # Process each row
            for idx in range(start_row, end_row):
                try:
                    row_start_time = time.time()
                    progress_pct = ((idx - start_row + 1) / (end_row - start_row)) * 100

                    # Calculate ETA
                    if row_times:
                        avg_time_per_row = sum(row_times) / len(row_times)
                        remaining_rows = end_row - idx - 1
                        eta_seconds = remaining_rows * avg_time_per_row
                        eta_minutes = eta_seconds / 60
                        if eta_minutes > 60:
                            eta_str = f"{eta_minutes/60:.1f}h"
                        elif eta_minutes > 1:
                            eta_str = f"{eta_minutes:.1f}m"
                        else:
                            eta_str = f"{eta_seconds:.0f}s"
                        logger.info(
                            f"🔄 Processing row {idx+1}/{total_rows} ({progress_pct:.1f}% complete, ETA: {eta_str})"
                        )
                    else:
                        logger.info(
                            f"🔄 Processing row {idx+1}/{total_rows} ({progress_pct:.1f}% complete)"
                        )

                    row = df.iloc[idx]
                    korean_term = row.get("Keyword/Concept", "Unknown")
                    logger.info(f"   📝 Korean term: {korean_term}")

                    result = self._process_row(row, idx)

                    # Track timing
                    row_end_time = time.time()
                    row_duration = row_end_time - row_start_time
                    row_times.append(row_duration)

                    # Keep only last 10 times for moving average
                    if len(row_times) > 10:
                        row_times = row_times[-10:]

                    # Update dataframe
                    if result:
                        df.at[idx, "keyword"] = result.get("keyword", "")
                        df.at[idx, "knowledge point"] = result.get(
                            "knowledge_point", ""
                        )
                        df.at[idx, "knowledge point source"] = result.get(
                            "source_url", ""
                        )
                        self.stats["successful_enhancements"] += 1
                        logger.info(
                            f"   ✅ Row {idx+1} enhanced successfully with keyword: {result.get('keyword', 'N/A')} ({row_duration:.1f}s)"
                        )
                    else:
                        logger.info(
                            f"   ❌ Row {idx+1} enhancement failed ({row_duration:.1f}s)"
                        )

                    # Heartbeat every 5 rows
                    if (idx + 1) % 5 == 0:
                        elapsed_time = time.time() - processing_start_time
                        avg_time = elapsed_time / (idx - start_row + 1)
                        logger.info(
                            f"💓 Heartbeat: {idx - start_row + 1} rows processed, avg {avg_time:.1f}s/row"
                        )

                    # Save progress every 10 rows
                    if (idx + 1) % 10 == 0:
                        logger.info(f"💾 Saving progress at row {idx+1}...")
                        df.to_csv(output_file, index=False)
                        success_rate = (
                            self.stats["successful_enhancements"]
                            / (idx - start_row + 1)
                        ) * 100
                        elapsed_time = time.time() - processing_start_time
                        logger.info(
                            f"   📊 Current success rate: {success_rate:.1f}% ({self.stats['successful_enhancements']}/{idx - start_row + 1} rows)"
                        )
                        logger.info(
                            f"   ⏱️  Total elapsed time: {elapsed_time/60:.1f} minutes"
                        )

                except Exception as e:
                    logger.error(f"❌ Error processing row {idx}: {str(e)}")
                    continue

            # Final save
            df.to_csv(output_file, index=False)
            logger.info(f"Enhancement complete. Output saved to: {output_file}")

            # Print statistics
            self._print_statistics()

            return True

        except Exception as e:
            logger.error(f"Error enhancing dataset: {str(e)}")
            return False

    def _process_row(self, row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """Process a single row with hybrid automated + manual fallback logic."""
        try:
            question = row.get("Question", "")
            korean_term = row.get("Keyword/Concept", "")
            manual_source = row.get("Knowledge Point Source", "").strip()

            if not question or not korean_term:
                logger.warning(f"   ⚠️  Row {row_idx}: Missing question or Korean term")
                return None

            logger.info(f"   🔍 Processing: '{korean_term}'")
            if manual_source:
                logger.info(f"   📋 Manual source available: {manual_source[:50]}...")

            # PRIMARY: Try automated Wikipedia search first
            logger.info(f"   🤖 Step 1: Attempting automated search...")

            # Generate keywords using LLM
            final_keyword, all_attempts = self.llm_handler.generate_keywords_iterative(
                question=question, korean_term=korean_term
            )

            if final_keyword:
                # Extract clean keyword
                clean_keyword = self.keyword_extractor.extract_keyword(final_keyword)

                if clean_keyword:
                    # Search Wikipedia with automated keyword
                    knowledge_point, source_url = (
                        self.wikipedia_handler.get_knowledge_point(clean_keyword)
                    )

                    if knowledge_point and source_url:
                        logger.info(f"   ✅ SUCCESS: Automated search found knowledge")
                        logger.info(
                            f"   🔄 Using automated results (overwriting manual data)"
                        )
                        self.stats["automated_success"] += 1
                        return {
                            "keyword": clean_keyword,
                            "knowledge_point": knowledge_point,
                            "source_url": source_url,
                            "llm_attempts": all_attempts,
                        }

            # FALLBACK: Try manual source if automated search failed
            logger.info(f"   ❌ Automated search failed, checking manual fallback...")

            if manual_source and "wikipedia.org" in manual_source.lower():
                logger.info(f"   📖 Step 2: Processing manual Wikipedia source...")
                fallback_knowledge = self._extract_from_manual_wikipedia_url(
                    manual_source
                )

                if fallback_knowledge:
                    logger.info(f"   ✅ SUCCESS: Manual Wikipedia source processed")
                    self.stats["manual_wikipedia_success"] += 1
                    return {
                        "keyword": korean_term,  # Use original Korean term as keyword
                        "knowledge_point": fallback_knowledge,
                        "source_url": manual_source,
                        "llm_attempts": (
                            all_attempts if "all_attempts" in locals() else []
                        ),
                    }
                else:
                    logger.warning(f"   ❌ Failed to extract from manual Wikipedia URL")

            elif manual_source and "wikipedia.org" not in manual_source.lower():
                logger.info(
                    f"   📄 Manual non-Wikipedia source detected - leaving as-is"
                )
                self.stats["manual_non_wikipedia_kept"] += 1
                return {
                    "keyword": korean_term,
                    "knowledge_point": row.get(
                        "knowledge point", ""
                    ),  # Keep existing if any
                    "source_url": manual_source,
                    "llm_attempts": all_attempts if "all_attempts" in locals() else [],
                }

            # FINAL FALLBACK: Generate knowledge using LLM
            logger.info(f"   🤖 Step 3: Generating knowledge using LLM...")
            generated_knowledge = self._generate_knowledge_with_llm(
                question, korean_term
            )

            if generated_knowledge:
                logger.info(f"   ✅ SUCCESS: Generated knowledge with LLM")
                self.stats["llm_generated_success"] += 1
                return {
                    "keyword": korean_term,
                    "knowledge_point": generated_knowledge,
                    "source_url": "generated",
                    "llm_attempts": all_attempts if "all_attempts" in locals() else [],
                }

            # Absolutely no sources worked
            logger.warning(f"   ❌ No knowledge found for: '{korean_term}'")
            self.stats["failed_wikipedia"] += 1
            return None

        except Exception as e:
            logger.error(f"   💥 Error processing row {row_idx}: {str(e)}")
            return None

    def _extract_from_manual_wikipedia_url(self, url: str) -> Optional[str]:
        """Extract knowledge point content from a manual Wikipedia URL."""
        try:
            logger.info(f"       🌐 Extracting content from: {url}")

            # Use requests to get the page content
            import requests
            from bs4 import BeautifulSoup
            import re

            response = requests.get(url, timeout=10)
            if response.status_code != 200:
                logger.warning(f"       ❌ Failed to fetch URL: {response.status_code}")
                return None

            soup = BeautifulSoup(response.text, "html.parser")

            # Find the main content
            content_div = soup.find("div", {"id": "mw-content-text"})
            if not content_div:
                logger.warning(f"       ❌ Could not find main content div")
                return None

            # Get the first few paragraphs
            paragraphs = content_div.find_all("p")
            content_parts = []

            for p in paragraphs[:3]:  # First 3 paragraphs
                text = p.get_text().strip()
                if text and len(text) > 50:  # Skip very short paragraphs
                    content_parts.append(text)
                    if len(" ".join(content_parts)) > 500:  # Limit total length
                        break

            if content_parts:
                content = " ".join(content_parts)

                # Clean the content
                content = re.sub(r"\[\d+\]", "", content)  # Remove citations
                content = re.sub(r"\[citation needed\]", "", content)
                content = re.sub(r"\[edit\]", "", content)
                content = " ".join(content.split())  # Clean whitespace

                # Limit to MAX_SENTENCES
                from config import MAX_SENTENCES

                sentences = re.split(r"[.!?]+", content)
                if len(sentences) > MAX_SENTENCES:
                    limited_sentences = sentences[:MAX_SENTENCES]
                    content = ". ".join(
                        s.strip() for s in limited_sentences if s.strip()
                    )
                    if content and not content.endswith("."):
                        content += "."

                logger.info(
                    f"       ✅ Extracted {len(content)} characters from manual URL"
                )
                return content

            logger.warning(f"       ❌ No suitable content found in manual URL")
            return None

        except Exception as e:
            logger.error(f"       💥 Error extracting from manual URL: {str(e)}")
            return None

    def _generate_knowledge_with_llm(
        self, question: str, korean_term: str
    ) -> Optional[str]:
        """Generate knowledge point using LLM when Wikipedia search fails."""
        try:
            logger.info(f"       🧠 Generating knowledge for '{korean_term}'...")

            # Create a prompt for knowledge generation
            prompt = f"""Based on the question and Korean cultural concept provided, generate a comprehensive knowledge point that explains the cultural significance, background, or context.

Question: {question}
Korean Concept: {korean_term}

Please provide a detailed explanation (2-3 sentences) that would help someone understand this Korean cultural element. Focus on:
- Historical or cultural background
- Significance in Korean society
- Key characteristics or features

Knowledge Point:"""

            # Use the LLM handler to generate knowledge
            response = self.llm_handler.generate_response(prompt)

            if response and len(response.strip()) > 50:  # Ensure substantial content
                # Clean and limit the response
                knowledge = response.strip()

                # Remove any unwanted prefixes
                if knowledge.startswith("Knowledge Point:"):
                    knowledge = knowledge[16:].strip()

                # Limit to reasonable length
                from config import MAX_SENTENCES
                import re

                sentences = re.split(r"[.!?]+", knowledge)
                if len(sentences) > MAX_SENTENCES:
                    limited_sentences = sentences[:MAX_SENTENCES]
                    knowledge = ". ".join(
                        s.strip() for s in limited_sentences if s.strip()
                    )
                    if knowledge and not knowledge.endswith("."):
                        knowledge += "."

                logger.info(
                    f"       ✅ Generated {len(knowledge)} characters of knowledge"
                )
                return knowledge
            else:
                logger.warning(f"       ❌ LLM generated insufficient content")
                return None

        except Exception as e:
            logger.error(f"       💥 Error generating knowledge with LLM: {str(e)}")
            return None

    def _print_statistics(self):
        """Print enhanced statistics with hybrid approach breakdown."""
        logger.info("=== Enhancement Statistics ===")
        logger.info(f"Total rows processed: {self.stats['total_rows']}")
        logger.info(f"Successful enhancements: {self.stats['successful_enhancements']}")

        # Breakdown by method
        logger.info("--- Success Breakdown ---")
        logger.info(f"Automated search success: {self.stats['automated_success']}")
        logger.info(
            f"Manual Wikipedia fallback: {self.stats['manual_wikipedia_success']}"
        )
        logger.info(
            f"Manual non-Wikipedia kept: {self.stats['manual_non_wikipedia_kept']}"
        )
        logger.info(f"LLM generated knowledge: {self.stats['llm_generated_success']}")

        # Failure breakdown
        logger.info("--- Failure Breakdown ---")
        logger.info(f"Failed LLM generation: {self.stats['failed_llm']}")
        logger.info(
            f"Failed keyword extraction: {self.stats['failed_keyword_extraction']}"
        )
        logger.info(f"Failed Wikipedia retrieval: {self.stats['failed_wikipedia']}")

        if self.stats["total_rows"] > 0:
            success_rate = (
                self.stats["successful_enhancements"] / self.stats["total_rows"]
            ) * 100
            automated_rate = (
                self.stats["automated_success"] / self.stats["total_rows"]
            ) * 100
            manual_wiki_rate = (
                self.stats["manual_wikipedia_success"] / self.stats["total_rows"]
            ) * 100
            manual_other_rate = (
                self.stats["manual_non_wikipedia_kept"] / self.stats["total_rows"]
            ) * 100
            llm_generated_rate = (
                self.stats["llm_generated_success"] / self.stats["total_rows"]
            ) * 100

            logger.info("--- Success Rates ---")
            logger.info(f"Overall success rate: {success_rate:.1f}%")
            logger.info(f"Automated search rate: {automated_rate:.1f}%")
            logger.info(f"Manual Wikipedia rate: {manual_wiki_rate:.1f}%")
            logger.info(f"Manual non-Wikipedia rate: {manual_other_rate:.1f}%")
            logger.info(f"LLM generated rate: {llm_generated_rate:.1f}%")

    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up resources")
        self.llm_handler.cleanup()


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="VQA Dataset Enhancement System")
    parser.add_argument("input_file", help="Input CSV file path")
    parser.add_argument("-o", "--output", help="Output CSV file path", default=None)
    parser.add_argument(
        "-s", "--start", type=int, default=0, help="Start row (for resuming)"
    )
    parser.add_argument(
        "-m", "--max", type=int, default=None, help="Maximum rows to process"
    )
    parser.add_argument(
        "--test", action="store_true", help="Run in test mode (first 5 rows)"
    )

    args = parser.parse_args()

    # Set output file
    if args.output is None:
        base_name = args.input_file.replace(".csv", "")
        args.output = f"{base_name}_enhanced.csv"

    # Test mode
    if args.test:
        args.max = 5
        logger.info("Running in test mode (5 rows)")

    # Initialize enhancer
    enhancer = VQAEnhancer()

    try:
        # Initialize components
        if not enhancer.initialize():
            logger.error("Failed to initialize system")
            return 1

        # Run enhancement
        success = enhancer.enhance_dataset(
            input_file=args.input_file,
            output_file=args.output,
            start_row=args.start,
            max_rows=args.max,
        )

        if success:
            logger.info("Enhancement completed successfully")
            return 0
        else:
            logger.error("Enhancement failed")
            return 1

    except KeyboardInterrupt:
        logger.info("Enhancement interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1
    finally:
        enhancer.cleanup()


if __name__ == "__main__":
    sys.exit(main())
