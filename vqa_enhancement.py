"""
VQA Dataset Enhancement System
Main script that orchestrates the entire process of adding Wikipedia-sourced knowledge
to Korean cultural questions using LLM-based keyword generation.
"""

import pandas as pd
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
import argparse

from config import LOG_LEVEL, LOG_FORMAT, LOG_FILE, OUTPUT_COLUMNS, BACKUP_SUFFIX
from llm_handler import LLMHandler
from wikipedia_handler import <PERSON><PERSON><PERSON><PERSON>
from keyword_extractor import KeywordExtractor

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VQAEnhancer:
    """Main class for VQA dataset enhancement."""
    
    def __init__(self):
        self.llm_handler = <PERSON><PERSON>Handler()
        self.wikipedia_handler = <PERSON><PERSON>andler()
        self.keyword_extractor = KeywordExtractor()
        self.stats = {
            "total_rows": 0,
            "successful_enhancements": 0,
            "failed_llm": 0,
            "failed_keyword_extraction": 0,
            "failed_wikipedia": 0,
            "generic_keywords_rejected": 0
        }
    
    def initialize(self) -> bool:
        """Initialize all components."""
        logger.info("Initializing VQA Enhancement System")
        
        # Test Wikipedia connection
        if not self.wikipedia_handler.test_connection():
            logger.error("Wikipedia connection test failed")
            return False
        
        # Initialize LLM model
        if not self.llm_handler.initialize_model():
            logger.warning("LLM model initialization failed - will skip LLM-based enhancement")
            return False
        
        # Test LLM model
        if not self.llm_handler.test_model():
            logger.error("LLM model test failed")
            return False
        
        logger.info("All components initialized successfully")
        return True
    
    def enhance_dataset(self, input_file: str, output_file: str, start_row: int = 0, max_rows: Optional[int] = None) -> bool:
        """
        Enhance the VQA dataset with Wikipedia knowledge.
        
        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file
            start_row: Row to start processing from (for resuming)
            max_rows: Maximum number of rows to process (None for all)
        """
        try:
            # Load dataset
            logger.info(f"Loading dataset from: {input_file}")
            df = pd.read_csv(input_file)
            
            # Create backup
            backup_file = input_file.replace('.csv', f'{BACKUP_SUFFIX}.csv')
            df.to_csv(backup_file, index=False)
            logger.info(f"Backup created: {backup_file}")
            
            # Initialize new columns if they don't exist
            for col in OUTPUT_COLUMNS:
                if col not in df.columns:
                    df[col] = ""
            
            # Determine processing range
            total_rows = len(df)
            end_row = min(start_row + max_rows, total_rows) if max_rows else total_rows
            
            logger.info(f"Processing rows {start_row} to {end_row-1} of {total_rows}")
            self.stats["total_rows"] = end_row - start_row
            
            # Process each row
            for idx in range(start_row, end_row):
                try:
                    logger.info(f"Processing row {idx+1}/{total_rows}")
                    
                    row = df.iloc[idx]
                    result = self._process_row(row, idx)
                    
                    # Update dataframe
                    if result:
                        df.at[idx, 'keyword'] = result.get('keyword', '')
                        df.at[idx, 'knowledge point'] = result.get('knowledge_point', '')
                        df.at[idx, 'knowledge point source'] = result.get('source_url', '')
                        self.stats["successful_enhancements"] += 1
                    
                    # Save progress every 10 rows
                    if (idx + 1) % 10 == 0:
                        df.to_csv(output_file, index=False)
                        logger.info(f"Progress saved at row {idx+1}")
                        
                except Exception as e:
                    logger.error(f"Error processing row {idx}: {str(e)}")
                    continue
            
            # Final save
            df.to_csv(output_file, index=False)
            logger.info(f"Enhancement complete. Output saved to: {output_file}")
            
            # Print statistics
            self._print_statistics()
            
            return True
            
        except Exception as e:
            logger.error(f"Error enhancing dataset: {str(e)}")
            return False
    
    def _process_row(self, row: pd.Series, row_idx: int) -> Optional[Dict[str, Any]]:
        """Process a single row of the dataset."""
        try:
            question = row.get('Question', '')
            korean_term = row.get('Keyword/Concept', '')
            
            if not question or not korean_term:
                logger.warning(f"Row {row_idx}: Missing question or Korean term")
                return None
            
            logger.info(f"Processing: {korean_term}")
            
            # Step 1: Generate keywords using LLM
            final_keyword, all_attempts = self.llm_handler.generate_keywords_iterative(
                question=question,
                korean_term=korean_term
            )
            
            if not final_keyword:
                logger.warning(f"Row {row_idx}: LLM failed to generate keyword")
                self.stats["failed_llm"] += 1
                return None
            
            # Step 2: Extract clean keyword
            clean_keyword = self.keyword_extractor.extract_keyword(final_keyword)
            
            if not clean_keyword:
                logger.warning(f"Row {row_idx}: Failed to extract valid keyword from: {final_keyword}")
                self.stats["failed_keyword_extraction"] += 1
                return None
            
            # Step 3: Get Wikipedia knowledge
            knowledge_point, source_url = self.wikipedia_handler.get_knowledge_point(clean_keyword)
            
            if not knowledge_point:
                logger.warning(f"Row {row_idx}: Failed to get Wikipedia content for: {clean_keyword}")
                self.stats["failed_wikipedia"] += 1
                return None
            
            logger.info(f"Row {row_idx}: Successfully enhanced with keyword: {clean_keyword}")
            
            return {
                'keyword': clean_keyword,
                'knowledge_point': knowledge_point,
                'source_url': source_url,
                'llm_attempts': all_attempts
            }
            
        except Exception as e:
            logger.error(f"Error processing row {row_idx}: {str(e)}")
            return None
    
    def _print_statistics(self):
        """Print enhancement statistics."""
        logger.info("=== Enhancement Statistics ===")
        logger.info(f"Total rows processed: {self.stats['total_rows']}")
        logger.info(f"Successful enhancements: {self.stats['successful_enhancements']}")
        logger.info(f"Failed LLM generation: {self.stats['failed_llm']}")
        logger.info(f"Failed keyword extraction: {self.stats['failed_keyword_extraction']}")
        logger.info(f"Failed Wikipedia retrieval: {self.stats['failed_wikipedia']}")
        
        if self.stats['total_rows'] > 0:
            success_rate = (self.stats['successful_enhancements'] / self.stats['total_rows']) * 100
            logger.info(f"Success rate: {success_rate:.1f}%")
    
    def cleanup(self):
        """Clean up resources."""
        logger.info("Cleaning up resources")
        self.llm_handler.cleanup()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="VQA Dataset Enhancement System")
    parser.add_argument("input_file", help="Input CSV file path")
    parser.add_argument("-o", "--output", help="Output CSV file path", default=None)
    parser.add_argument("-s", "--start", type=int, default=0, help="Start row (for resuming)")
    parser.add_argument("-m", "--max", type=int, default=None, help="Maximum rows to process")
    parser.add_argument("--test", action="store_true", help="Run in test mode (first 5 rows)")
    
    args = parser.parse_args()
    
    # Set output file
    if args.output is None:
        base_name = args.input_file.replace('.csv', '')
        args.output = f"{base_name}_enhanced.csv"
    
    # Test mode
    if args.test:
        args.max = 5
        logger.info("Running in test mode (5 rows)")
    
    # Initialize enhancer
    enhancer = VQAEnhancer()
    
    try:
        # Initialize components
        if not enhancer.initialize():
            logger.error("Failed to initialize system")
            return 1
        
        # Run enhancement
        success = enhancer.enhance_dataset(
            input_file=args.input_file,
            output_file=args.output,
            start_row=args.start,
            max_rows=args.max
        )
        
        if success:
            logger.info("Enhancement completed successfully")
            return 0
        else:
            logger.error("Enhancement failed")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Enhancement interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1
    finally:
        enhancer.cleanup()

if __name__ == "__main__":
    sys.exit(main())
