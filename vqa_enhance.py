#!/usr/bin/env python3
"""
VQA Enhancer CLI
Command-line interface for enhancing VQA datasets with Korean cultural knowledge.
"""

import fire
import sys
import os
import logging
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from vqa_enhancer import VQAEnhancer, VisionVQAGenerator


class VQAEnhancerCLI:
    """Command-line interface for VQA dataset enhancement."""

    def __init__(self):
        """Initialize the CLI."""
        self.enhancer = None

    def enhance(
        self,
        input_file: str,
        output_file: str = None,
        test: bool = False,
        rows: int = None,
        gpu_ids: str = None,
        log_level: str = "INFO",
    ):
        """
        Enhance a VQA dataset with Korean cultural knowledge.

        Args:
            input_file: Path to input CSV file
            output_file: Path to output CSV file (default: input_file with _enhanced suffix)
            test: Run in test mode (process only first 5 rows)
            rows: Number of rows to process (overrides test mode)
            gpu_ids: Comma-separated GPU IDs to use (e.g., "0,1,2,3")
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)

        Examples:
            # Basic enhancement
            python vqa_enhance.py enhance data/vqa.csv

            # Test mode (first 5 rows)
            python vqa_enhance.py enhance data/vqa.csv --test

            # Process specific number of rows
            python vqa_enhance.py enhance data/vqa.csv --rows 10

            # Use specific GPUs
            python vqa_enhance.py enhance data/vqa.csv --gpu_ids "0,1,2,3"

            # Custom output file
            python vqa_enhance.py enhance data/vqa.csv data/enhanced_vqa.csv
        """
        # Set up logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format="%(asctime)s - %(levelname)s - %(message)s",
        )

        # Set GPU environment if specified
        if gpu_ids:
            os.environ["CUDA_VISIBLE_DEVICES"] = gpu_ids
            print(f"🔧 Using GPUs: {gpu_ids}")

        # Determine output file
        if output_file is None:
            input_path = Path(input_file)
            output_file = str(
                input_path.parent / f"{input_path.stem}_enhanced{input_path.suffix}"
            )

        # Determine number of rows to process
        if test and rows is None:
            rows = 5
            print("🧪 Test mode: Processing first 5 rows")
        elif rows:
            print(f"📊 Processing {rows} rows")

        print(f"📂 Input: {input_file}")
        print(f"📂 Output: {output_file}")

        # Initialize and run enhancer
        self.enhancer = VQAEnhancer()

        try:
            self.enhancer.enhance_dataset(
                input_file=input_file, output_file=output_file, max_rows=rows
            )
            print(f"✅ Enhancement completed successfully!")
            print(f"📄 Enhanced dataset saved to: {output_file}")

        except Exception as e:
            print(f"❌ Enhancement failed: {str(e)}")
            sys.exit(1)
        finally:
            if self.enhancer:
                self.enhancer.cleanup()

    def validate(self, csv_file: str):
        """
        Validate CSV file format for VQA enhancement.

        Args:
            csv_file: Path to CSV file to validate

        Example:
            python vqa_enhance.py validate data/vqa.csv
        """
        import pandas as pd

        try:
            print(f"🔍 Validating CSV file: {csv_file}")

            # Load CSV
            df = pd.read_csv(csv_file)
            print(f"✅ Successfully loaded {len(df)} rows")

            # Check required columns
            required_columns = ["Question", "Keyword/Concept"]
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                print(f"❌ Missing required columns: {missing_columns}")
                print(f"📋 Required columns: {required_columns}")
                print(f"📋 Found columns: {list(df.columns)}")
                return False

            print(f"✅ All required columns present")

            # Check optional columns
            optional_columns = ["knowledge point", "knowledge point source"]
            for col in optional_columns:
                if col in df.columns:
                    empty_count = df[col].isna().sum() + (df[col] == "").sum()
                    print(f"📊 Column '{col}': {empty_count}/{len(df)} empty")
                else:
                    print(f"ℹ️  Optional column '{col}' not found (will be created)")

            # Show sample data
            print(f"\n📋 Sample data:")
            for i in range(min(3, len(df))):
                row = df.iloc[i]
                print(f"  Row {i+1}:")
                print(f"    Question: {row['Question'][:60]}...")
                print(f"    Keyword/Concept: {row['Keyword/Concept']}")

            print(f"\n✅ CSV file is valid for VQA enhancement!")
            return True

        except Exception as e:
            print(f"❌ Validation failed: {str(e)}")
            return False

    def info(self):
        """
        Display system information and requirements.

        Example:
            python vqa_enhance.py info
        """
        print("🚀 VQA Enhancer System Information")
        print("=" * 50)

        # System info
        import torch

        print(f"🐍 Python version: {sys.version}")
        print(f"🔥 PyTorch version: {torch.__version__}")
        print(f"🔧 CUDA available: {torch.cuda.is_available()}")

        if torch.cuda.is_available():
            print(f"🎮 GPU count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

        # Package info
        print(f"\n📦 VQA Enhancer Features:")
        print(f"   ✅ Enhanced Wikipedia search with multiple strategies")
        print(f"   ✅ LLM-based knowledge generation fallback")
        print(f"   ✅ Multi-GPU support for faster processing")
        print(f"   ✅ Hybrid automated + manual source processing")
        print(f"   ✅ Korean cultural content validation")
        print(f"   ✅ Comprehensive statistics and logging")

        print(f"\n📋 Supported CSV Format:")
        print(f"   Required columns: Question, Keyword/Concept")
        print(f"   Optional columns: knowledge point, knowledge point source")

    def example(self):
        """
        Create example CSV files for testing.

        Example:
            python vqa_enhance.py example
        """
        import pandas as pd

        # Create example data
        example_data = [
            {
                "Question": "What is this traditional Korean palace?",
                "Keyword/Concept": "Gyeongbokgung Palace",
                "knowledge point": "",
                "knowledge point source": "",
            },
            {
                "Question": "What is this Korean meme phrase?",
                "Keyword/Concept": "1루수가 누구야",
                "knowledge point": "",
                "knowledge point source": "",
            },
            {
                "Question": "What is this famous tower in Seoul?",
                "Keyword/Concept": "N Seoul Tower",
                "knowledge point": "",
                "knowledge point source": "https://en.wikipedia.org/wiki/N_Seoul_Tower",
            },
            {
                "Question": "What is this Korean cultural concept?",
                "Keyword/Concept": "빨리빨리 문화",
                "knowledge point": "",
                "knowledge point source": "",
            },
        ]

        df = pd.DataFrame(example_data)

        # Create examples directory if it doesn't exist
        os.makedirs("examples", exist_ok=True)

        # Save example file
        example_file = "examples/example_vqa.csv"
        df.to_csv(example_file, index=False)

        print(f"✅ Created example CSV file: {example_file}")
        print(f"📋 Contains {len(df)} sample rows")
        print(f"🚀 Test with: python vqa_enhance.py enhance {example_file} --test")

    def generate_vision_vqa(
        self,
        input_file: str,
        output_file: str = None,
        test: bool = False,
        rows: int = None,
        log_level: str = "INFO",
    ):
        """
        Generate Korean cultural VQA questions using OpenAI's GPT-4o-mini vision model for VLLM benchmarking.

        Args:
            input_file: Path to input CSV file with Image Source column
            output_file: Path to output CSV file (default: input_file with _vision_vqa suffix)
            test: Run in test mode (process only first 5 rows)
            rows: Number of rows to process (overrides test mode)
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR)

        Examples:
            # Basic vision VQA generation
            python vqa_enhance.py generate-vision-vqa data/vqa.csv

            # Test mode (first 5 rows)
            python vqa_enhance.py generate-vision-vqa data/vqa.csv --test

            # Process specific number of rows
            python vqa_enhance.py generate-vision-vqa data/vqa.csv --rows 10

            # Custom output file
            python vqa_enhance.py generate-vision-vqa data/vqa.csv data/vision_vqa.csv
        """
        # Set up logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format="%(asctime)s - %(levelname)s - %(message)s",
        )

        # Determine output file
        if output_file is None:
            input_path = Path(input_file)
            output_file = str(
                input_path.parent / f"{input_path.stem}_vision_vqa{input_path.suffix}"
            )

        # Determine number of rows to process
        if test and rows is None:
            rows = 5
            print("🧪 Test mode: Processing first 5 rows")
        elif rows:
            print(f"📊 Processing {rows} rows")

        print(f"📂 Input: {input_file}")
        print(f"📂 Output: {output_file}")

        # Initialize and run vision VQA generator
        generator = VisionVQAGenerator()

        try:
            if not generator.initialize():
                print("❌ Failed to initialize Vision VQA Generator")
                print("💡 Make sure OPENAI_API_KEY is set in your .env file")
                sys.exit(1)

            success = generator.generate_vqa_dataset(
                input_file=input_file, output_file=output_file, max_rows=rows
            )

            if success:
                print(f"✅ Vision VQA generation completed successfully!")
                print(f"📄 Generated dataset saved to: {output_file}")
            else:
                print(f"❌ Vision VQA generation failed")
                sys.exit(1)

        except Exception as e:
            print(f"❌ Vision VQA generation failed: {str(e)}")
            sys.exit(1)

    def test_vision_generation(
        self, image_url: str, keyword: str, category: str = "Test"
    ):
        """
        Test vision VQA generation with a single image.

        Args:
            image_url: URL of the image to test
            keyword: Korean cultural keyword/concept
            category: Category for the test

        Example:
            python vqa_enhance.py test-vision-generation "https://example.com/image.jpg" "한복" "Clothing"
        """
        logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")

        generator = VisionVQAGenerator()

        try:
            if not generator.initialize():
                print("❌ Failed to initialize Vision VQA Generator")
                return

            success = generator.test_single_generation(image_url, keyword, category)

            if success:
                print("🎉 Test completed successfully!")
            else:
                print("❌ Test failed")

        except Exception as e:
            print(f"❌ Test failed: {str(e)}")


def main():
    """Main entry point for the CLI."""
    fire.Fire(VQAEnhancerCLI)


if __name__ == "__main__":
    main()
