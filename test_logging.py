"""
Test script to demonstrate enhanced logging without requiring LLM model.
This simulates the VQA enhancement process with detailed logging.
"""

import pandas as pd
import logging
import time
import random
from keyword_extractor import KeywordExtractor
from wikipedia_handler import WikipediaHandler

# Configure logging to show all the enhanced messages
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_logging.log')
    ]
)
logger = logging.getLogger(__name__)

def simulate_llm_response(korean_term: str) -> str:
    """Simulate LLM responses for testing."""
    responses = {
        "제주 돌집": "Title: Jeju Stone House",
        "운현궁": "Unhyeongung Palace",
        "월정교": "Title: Woljeonggyo",
        "경복궁": "Gyeongbokgung",
        "불국사": "\"Bulguksa Temple\"",
        "첨성대": "I recommend Cheomseongdae Observatory",
        "한옥마을": "Korean traditional village",  # Should be rejected
        "남산타워": "Title: N Seoul Tower",
        "신라대종": "Emille Bell",
        "고려대학교": "Korea University"
    }
    
    # Add some randomness to simulate real LLM behavior
    time.sleep(random.uniform(0.5, 2.0))  # Simulate LLM inference time
    
    return responses.get(korean_term, f"Title: {korean_term}")

def simulate_processing():
    """Simulate the VQA enhancement process with enhanced logging."""
    logger.info("🚀 Starting VQA Enhancement System Test")
    
    # Initialize components
    logger.info("📡 Step 1/3: Testing Wikipedia connection...")
    wikipedia_handler = WikipediaHandler()
    if not wikipedia_handler.test_connection():
        logger.error("❌ Wikipedia connection test failed")
        return
    logger.info("✅ Wikipedia connection successful")
    
    logger.info("🔍 Step 2/3: Initializing keyword extractor...")
    keyword_extractor = KeywordExtractor()
    logger.info("✅ Keyword extractor ready")
    
    logger.info("🧪 Step 3/3: Testing components...")
    logger.info("✅ All components initialized successfully")
    
    # Load sample data
    try:
        df = pd.read_csv("VQA.csv")
        sample_rows = df.head(10)  # Process first 10 rows
        logger.info(f"📋 Processing 10 sample rows from VQA.csv")
    except FileNotFoundError:
        # Create sample data if VQA.csv doesn't exist
        sample_data = {
            'Keyword/Concept': ['제주 돌집', '운현궁', '월정교', '경복궁', '불국사', 
                               '첨성대', '한옥마을', '남산타워', '신라대종', '고려대학교'],
            'Question': ['Sample question about ' + term for term in 
                        ['제주 돌집', '운현궁', '월정교', '경복궁', '불국사', 
                         '첨성대', '한옥마을', '남산타워', '신라대종', '고려대학교']]
        }
        sample_rows = pd.DataFrame(sample_data)
        logger.info(f"📋 Processing 10 simulated rows (VQA.csv not found)")
    
    # Track timing
    processing_start_time = time.time()
    row_times = []
    successful_enhancements = 0
    
    # Process each row
    for idx, row in sample_rows.iterrows():
        try:
            row_start_time = time.time()
            progress_pct = ((idx + 1) / len(sample_rows)) * 100
            
            # Calculate ETA
            if row_times:
                avg_time_per_row = sum(row_times) / len(row_times)
                remaining_rows = len(sample_rows) - idx - 1
                eta_seconds = remaining_rows * avg_time_per_row
                eta_minutes = eta_seconds / 60
                if eta_minutes > 1:
                    eta_str = f"{eta_minutes:.1f}m"
                else:
                    eta_str = f"{eta_seconds:.0f}s"
                logger.info(f"🔄 Processing row {idx+1}/{len(sample_rows)} ({progress_pct:.1f}% complete, ETA: {eta_str})")
            else:
                logger.info(f"🔄 Processing row {idx+1}/{len(sample_rows)} ({progress_pct:.1f}% complete)")
            
            korean_term = row['Keyword/Concept']
            logger.info(f"   📝 Korean term: {korean_term}")
            
            # Step 1: Simulate LLM generation
            logger.info(f"   🤖 Step 1/3: Generating keywords using LLM for '{korean_term}'...")
            logger.info(f"     🧠 Starting LLM keyword generation for '{korean_term}'")
            logger.info(f"     🎯 LLM Attempt 1/3 (temperature: 0.7)")
            logger.info(f"     📝 Using prompt strategy: attempt_1")
            logger.info(f"     ⏳ Generating response...")
            
            llm_response = simulate_llm_response(korean_term)
            
            logger.info(f"     ⏱️  LLM inference completed in {random.uniform(1.0, 3.0):.2f} seconds")
            logger.info(f"     📤 Generated {len(llm_response)} characters of response")
            logger.info(f"     ✅ LLM attempt 1 generated: {llm_response[:80]}...")
            logger.info(f"     🎉 Found valid response on attempt 1, stopping iteration")
            
            # Step 2: Extract keyword
            logger.info(f"   🔍 Step 2/3: Extracting clean keyword from LLM response...")
            logger.info(f"     🔍 Extracting keyword from response: {llm_response[:60]}...")
            logger.info(f"     🎯 Trying extraction patterns...")
            
            keyword = keyword_extractor.extract_keyword(llm_response)
            
            if not keyword:
                logger.warning(f"   ❌ Row {idx}: Failed to extract valid keyword")
                row_end_time = time.time()
                row_duration = row_end_time - row_start_time
                row_times.append(row_duration)
                logger.info(f"   ❌ Row {idx+1} enhancement failed ({row_duration:.1f}s)")
                continue
            
            logger.info(f"     📝 Raw keyword extracted: '{keyword}'")
            logger.info(f"     🎉 Successfully extracted valid keyword: '{keyword}'")
            
            # Step 3: Get Wikipedia content
            logger.info(f"   📚 Step 3/3: Searching Wikipedia for '{keyword}'...")
            
            knowledge_point, source_url = wikipedia_handler.get_knowledge_point(keyword)
            
            row_end_time = time.time()
            row_duration = row_end_time - row_start_time
            row_times.append(row_duration)
            
            # Keep only last 10 times for moving average
            if len(row_times) > 10:
                row_times = row_times[-10:]
            
            if knowledge_point:
                successful_enhancements += 1
                logger.info(f"   🎉 Row {idx+1}: Successfully enhanced! '{korean_term}' → '{keyword}'")
                logger.info(f"   📄 Knowledge preview: {knowledge_point[:80]}...")
                logger.info(f"   ✅ Row {idx+1} enhanced successfully with keyword: {keyword} ({row_duration:.1f}s)")
            else:
                logger.info(f"   ❌ Row {idx+1} enhancement failed ({row_duration:.1f}s)")
            
            # Heartbeat every 5 rows
            if (idx + 1) % 5 == 0:
                elapsed_time = time.time() - processing_start_time
                avg_time = elapsed_time / (idx + 1)
                logger.info(f"💓 Heartbeat: {idx + 1} rows processed, avg {avg_time:.1f}s/row")
            
        except Exception as e:
            logger.error(f"❌ Error processing row {idx}: {str(e)}")
            continue
    
    # Final statistics
    total_time = time.time() - processing_start_time
    success_rate = (successful_enhancements / len(sample_rows)) * 100
    
    logger.info("=== Enhancement Statistics ===")
    logger.info(f"Total rows processed: {len(sample_rows)}")
    logger.info(f"Successful enhancements: {successful_enhancements}")
    logger.info(f"Success rate: {success_rate:.1f}%")
    logger.info(f"Total processing time: {total_time:.1f} seconds")
    logger.info(f"Average time per row: {total_time/len(sample_rows):.1f} seconds")
    logger.info("🎉 Test completed!")

if __name__ == "__main__":
    simulate_processing()
