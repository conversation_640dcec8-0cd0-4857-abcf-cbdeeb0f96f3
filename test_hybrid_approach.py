"""
Test script for the hybrid automated + manual fallback approach.
Tests the new logic with different scenarios.
"""

import logging
import pandas as pd
from vqa_enhancement import VQAEnhancer

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data with different scenarios."""
    test_data = [
        {
            'Question': 'What is this traditional Korean palace?',
            'Keyword/Concept': 'Gyeongbokgung Palace',
            'Knowledge Point Source': '',  # No manual source - should use automated
            'knowledge point': ''
        },
        {
            'Question': 'What is this famous tower in Seoul?',
            'Keyword/Concept': 'N Seoul Tower',
            'Knowledge Point Source': 'https://en.wikipedia.org/wiki/N_Seoul_Tower',  # Manual Wikipedia - should try automated first, then fallback
            'knowledge point': ''
        },
        {
            'Question': 'What is this traditional Korean temple?',
            'Keyword/Concept': 'Some Unknown Temple',
            'Knowledge Point Source': 'https://en.wikipedia.org/wiki/Bulguksa',  # Manual Wikipedia - automated should fail, use manual
            'knowledge point': ''
        },
        {
            'Question': 'What is this Korean cultural site?',
            'Keyword/Concept': 'Some Cultural Site',
            'Knowledge Point Source': 'https://www.koreanculture.org/some-site',  # Manual non-Wikipedia - should keep as-is
            'knowledge point': 'This is manually provided knowledge about the cultural site.'
        },
        {
            'Question': 'What is this Korean landmark?',
            'Keyword/Concept': 'Unknown Landmark',
            'Knowledge Point Source': '',  # No manual source, automated should fail
            'knowledge point': ''
        }
    ]
    
    return pd.DataFrame(test_data)

def test_hybrid_approach():
    """Test the hybrid approach with different scenarios."""
    
    logger.info("🧪 Testing Hybrid Automated + Manual Fallback Approach")
    logger.info("=" * 60)
    
    # Create test data
    test_df = create_test_data()
    
    # Save test data
    test_file = "test_hybrid_data.csv"
    test_df.to_csv(test_file, index=False)
    logger.info(f"Created test data: {test_file}")
    
    # Initialize enhancer
    enhancer = VQAEnhancer()
    
    try:
        # Initialize components (skip LLM for faster testing)
        logger.info("🚀 Initializing system...")
        if not enhancer.wikipedia_handler.test_connection():
            logger.error("❌ Wikipedia connection test failed")
            return False
        
        logger.info("✅ Wikipedia connection successful")
        
        # Test each row manually to see the logic in action
        logger.info("\n🔍 Testing each scenario:")
        logger.info("=" * 50)
        
        for idx, row in test_df.iterrows():
            logger.info(f"\n📋 Test Case {idx + 1}:")
            logger.info(f"   Korean Term: {row['Keyword/Concept']}")
            logger.info(f"   Manual Source: {row['Knowledge Point Source'] or 'None'}")
            logger.info(f"   Existing Knowledge: {row['knowledge point'] or 'None'}")
            
            # Test the processing logic (without LLM for speed)
            result = test_row_processing(enhancer, row, idx)
            
            if result:
                logger.info(f"   ✅ Result: {result['keyword']}")
                logger.info(f"   📄 Knowledge: {result['knowledge_point'][:80]}...")
                logger.info(f"   🔗 Source: {result['source_url']}")
            else:
                logger.info(f"   ❌ No result")
        
        logger.info(f"\n📊 Final Statistics:")
        enhancer._print_statistics()
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Test failed: {str(e)}")
        return False

def test_row_processing(enhancer, row, idx):
    """Test row processing without full LLM pipeline."""
    try:
        korean_term = row.get('Keyword/Concept', '')
        manual_source = row.get('Knowledge Point Source', '').strip()
        
        logger.info(f"   🔍 Processing: '{korean_term}'")
        if manual_source:
            logger.info(f"   📋 Manual source available: {manual_source[:50]}...")
        
        # For testing, simulate automated search results
        # In real scenario, this would go through LLM -> keyword extraction -> Wikipedia search
        automated_success = simulate_automated_search(korean_term)
        
        if automated_success:
            knowledge_point, source_url = automated_success
            logger.info(f"   ✅ SUCCESS: Simulated automated search found knowledge")
            logger.info(f"   🔄 Using automated results (overwriting manual data)")
            enhancer.stats["automated_success"] += 1
            enhancer.stats["successful_enhancements"] += 1
            return {
                "keyword": korean_term,
                "knowledge_point": knowledge_point,
                "source_url": source_url,
                "llm_attempts": []
            }
        
        # FALLBACK: Try manual source if automated search failed
        logger.info(f"   ❌ Automated search failed, checking manual fallback...")
        
        if manual_source and 'wikipedia.org' in manual_source.lower():
            logger.info(f"   📖 Step 2: Processing manual Wikipedia source...")
            fallback_knowledge = enhancer._extract_from_manual_wikipedia_url(manual_source)
            
            if fallback_knowledge:
                logger.info(f"   ✅ SUCCESS: Manual Wikipedia source processed")
                enhancer.stats["manual_wikipedia_success"] += 1
                enhancer.stats["successful_enhancements"] += 1
                return {
                    "keyword": korean_term,
                    "knowledge_point": fallback_knowledge,
                    "source_url": manual_source,
                    "llm_attempts": []
                }
            else:
                logger.warning(f"   ❌ Failed to extract from manual Wikipedia URL")
        
        elif manual_source and 'wikipedia.org' not in manual_source.lower():
            logger.info(f"   📄 Manual non-Wikipedia source detected - leaving as-is")
            enhancer.stats["manual_non_wikipedia_kept"] += 1
            enhancer.stats["successful_enhancements"] += 1
            return {
                "keyword": korean_term,
                "knowledge_point": row.get('knowledge point', ''),
                "source_url": manual_source,
                "llm_attempts": []
            }
        
        # No automated or manual sources worked
        logger.warning(f"   ❌ No knowledge found for: '{korean_term}'")
        enhancer.stats["failed_wikipedia"] += 1
        return None
        
    except Exception as e:
        logger.error(f"   💥 Error processing row {idx}: {str(e)}")
        return None

def simulate_automated_search(korean_term):
    """Simulate automated search results for testing."""
    # Simulate that some terms work with automated search
    success_terms = ['Gyeongbokgung Palace', 'N Seoul Tower']
    
    if korean_term in success_terms:
        return f"Simulated knowledge about {korean_term}. This would be the actual Wikipedia content.", f"https://en.wikipedia.org/wiki/{korean_term.replace(' ', '_')}"
    
    return None

def main():
    """Run the hybrid approach test."""
    logger.info("🚀 Hybrid Approach Testing Suite")
    logger.info("=" * 50)
    
    success = test_hybrid_approach()
    
    if success:
        logger.info("\n🎉 Hybrid approach test completed successfully!")
        logger.info("✅ The system is ready to use automated + manual fallback logic")
    else:
        logger.info("\n❌ Hybrid approach test failed")
        logger.info("⚠️  Check the implementation before running on full dataset")

if __name__ == "__main__":
    main()
