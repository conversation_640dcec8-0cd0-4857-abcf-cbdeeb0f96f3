"""
Test script for the new Web Knowledge Handler
Tests the web crawler approach for finding Korean cultural information.
"""

import logging
from wikipedia_handler import WebKnowledgeHandler

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_web_crawler():
    """Test the web crawler with challenging cases."""
    
    handler = WebKnowledgeHandler()
    
    # Test cases that were failing with Wikipedia API
    test_cases = [
        "N Seoul Tower",
        "Seoul Tower", 
        "Namsan Tower",
        "DDP",
        "Dongdaemun Design Plaza",
        "Gyeongbokgung",
        "Bulguksa",
        "Cheomseongdae",
        "Jeju Stone House",
        "Woljeonggyo",
        "Unhyeongung",
        "Myeongdong",
        "Insadong",
        "Bukchon Hanok Village"
    ]
    
    logger.info("🌐 Testing Web Knowledge Handler")
    logger.info("=" * 50)
    
    successful = 0
    failed = 0
    
    for keyword in test_cases:
        logger.info(f"\n🔍 Testing: '{keyword}'")
        logger.info("-" * 30)
        
        try:
            knowledge_point, source_url = handler.get_knowledge_point(keyword)
            
            if knowledge_point and source_url:
                logger.info(f"✅ SUCCESS: {keyword}")
                logger.info(f"   📄 Content: {knowledge_point[:80]}...")
                logger.info(f"   🔗 Source: {source_url}")
                successful += 1
            else:
                logger.warning(f"❌ FAILED: {keyword}")
                failed += 1
                
        except Exception as e:
            logger.error(f"💥 ERROR: {keyword} - {str(e)}")
            failed += 1
    
    total = successful + failed
    success_rate = (successful / total) * 100 if total > 0 else 0
    
    logger.info(f"\n📊 Web Crawler Results:")
    logger.info(f"   Successful: {successful}/{total}")
    logger.info(f"   Success rate: {success_rate:.1f}%")
    
    if success_rate > 80:
        logger.info("🎉 Excellent! Web crawler is working very well")
    elif success_rate > 60:
        logger.info("👍 Good! Web crawler shows improvement")
    elif success_rate > 40:
        logger.info("📈 Moderate success with web crawler")
    else:
        logger.info("⚠️  Web crawler needs improvement")
    
    return success_rate

def test_specific_cases():
    """Test specific cases that should definitely work."""
    
    handler = WebKnowledgeHandler()
    
    # These should definitely be findable
    guaranteed_cases = [
        "Gyeongbokgung Palace",
        "Bulguksa Temple", 
        "N Seoul Tower",
        "Jeju Island"
    ]
    
    logger.info("\n🎯 Testing Guaranteed Cases")
    logger.info("=" * 40)
    
    for keyword in guaranteed_cases:
        logger.info(f"\n🔍 Testing guaranteed case: '{keyword}'")
        
        try:
            knowledge_point, source_url = handler.get_knowledge_point(keyword)
            
            if knowledge_point and source_url:
                logger.info(f"✅ SUCCESS: {keyword}")
                logger.info(f"   📄 {knowledge_point[:100]}...")
            else:
                logger.error(f"❌ FAILED: {keyword} (This should have worked!)")
                
        except Exception as e:
            logger.error(f"💥 ERROR: {keyword} - {str(e)}")

def main():
    """Run all tests."""
    logger.info("🚀 Web Knowledge Handler Testing")
    logger.info("=" * 50)
    
    # Test connection first
    handler = WebKnowledgeHandler()
    if handler.test_connection():
        logger.info("✅ Connection test passed")
    else:
        logger.error("❌ Connection test failed")
        return
    
    # Test general cases
    success_rate = test_web_crawler()
    
    # Test guaranteed cases
    test_specific_cases()
    
    logger.info(f"\n🎉 Testing completed!")
    logger.info(f"Overall success rate: {success_rate:.1f}%")
    
    if success_rate > 70:
        logger.info("✅ Web crawler is ready for production!")
        logger.info("🚀 You can now run: python vqa_enhancement.py VQA.csv --test")
    else:
        logger.info("⚠️  Consider further improvements")

if __name__ == "__main__":
    main()
