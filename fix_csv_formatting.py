"""
CSV Formatting Fix Script for VQA Enhanced Dataset
Identifies and fixes formatting issues in the enhanced CSV file.
"""

import pandas as pd
import logging
import sys
import re
from typing import Dict, List, Tuple

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def analyze_csv_issues(df: pd.DataFrame) -> Dict[str, int]:
    """Analyze the CSV file for various formatting issues."""
    issues = {
        'total_rows': len(df),
        'missing_keywords': 0,
        'missing_knowledge': 0,
        'missing_sources': 0,
        'empty_enhancement_rows': 0,
        'mismatched_keywords': 0,
        'malformed_urls': 0,
        'duplicate_rows': 0
    }
    
    # Check for missing data
    for idx, row in df.iterrows():
        keyword = str(row.get('keyword', '')).strip()
        knowledge = str(row.get('knowledge point', '')).strip()
        source = str(row.get('knowledge point source', '')).strip()
        
        if not keyword or keyword == 'nan':
            issues['missing_keywords'] += 1
        if not knowledge or knowledge == 'nan':
            issues['missing_knowledge'] += 1
        if not source or source == 'nan':
            issues['missing_sources'] += 1
        
        # Check if entire enhancement is empty
        if (not keyword or keyword == 'nan') and \
           (not knowledge or knowledge == 'nan') and \
           (not source or source == 'nan'):
            issues['empty_enhancement_rows'] += 1
        
        # Check for malformed URLs
        if source and source != 'nan' and not source.startswith('http'):
            issues['malformed_urls'] += 1
    
    # Check for duplicates
    issues['duplicate_rows'] = len(df) - len(df.drop_duplicates())
    
    return issues

def fix_csv_formatting(input_file: str, output_file: str) -> bool:
    """Fix formatting issues in the CSV file."""
    try:
        logger.info(f"📋 Loading CSV file: {input_file}")
        df = pd.read_csv(input_file)
        
        # Analyze issues
        logger.info("🔍 Analyzing formatting issues...")
        issues = analyze_csv_issues(df)
        
        logger.info("📊 Issues found:")
        for issue, count in issues.items():
            if count > 0:
                logger.info(f"   {issue}: {count}")
        
        # Fix issues
        logger.info("🔧 Fixing formatting issues...")
        
        # 1. Clean up NaN values
        df = df.fillna('')
        
        # 2. Remove duplicate rows
        original_len = len(df)
        df = df.drop_duplicates()
        if len(df) < original_len:
            logger.info(f"   ✅ Removed {original_len - len(df)} duplicate rows")
        
        # 3. Clean up whitespace
        for col in df.columns:
            if df[col].dtype == 'object':
                df[col] = df[col].astype(str).str.strip()
        
        # 4. Fix malformed URLs
        url_fixes = 0
        for idx, row in df.iterrows():
            source = str(row.get('knowledge point source', '')).strip()
            if source and source != '' and not source.startswith('http'):
                # Try to fix common URL issues
                if source.startswith('www.'):
                    df.at[idx, 'knowledge point source'] = f"https://{source}"
                    url_fixes += 1
                elif 'wikipedia' in source.lower() and not source.startswith('http'):
                    df.at[idx, 'knowledge point source'] = f"https://en.wikipedia.org/wiki/{source}"
                    url_fixes += 1
        
        if url_fixes > 0:
            logger.info(f"   ✅ Fixed {url_fixes} malformed URLs")
        
        # 5. Validate and clean knowledge points
        knowledge_fixes = 0
        for idx, row in df.iterrows():
            knowledge = str(row.get('knowledge point', '')).strip()
            if knowledge and len(knowledge) > 1000:  # Truncate very long knowledge points
                df.at[idx, 'knowledge point'] = knowledge[:997] + "..."
                knowledge_fixes += 1
        
        if knowledge_fixes > 0:
            logger.info(f"   ✅ Truncated {knowledge_fixes} overly long knowledge points")
        
        # 6. Ensure proper column order
        expected_columns = [
            'Main Category', 'Question', 'Option 1', 'Option 2', 'Option 3', 'Option 4',
            'Correct Option', 'Keyword/Concept', 'keyword', 'knowledge point', 'knowledge point source'
        ]
        
        # Reorder columns if they exist
        existing_columns = [col for col in expected_columns if col in df.columns]
        df = df[existing_columns]
        
        # Save fixed file
        logger.info(f"💾 Saving fixed CSV to: {output_file}")
        df.to_csv(output_file, index=False, encoding='utf-8')
        
        # Final analysis
        logger.info("🔍 Final analysis after fixes:")
        final_issues = analyze_csv_issues(df)
        for issue, count in final_issues.items():
            logger.info(f"   {issue}: {count}")
        
        # Calculate completion rate
        total_rows = final_issues['total_rows']
        enhanced_rows = total_rows - final_issues['empty_enhancement_rows']
        completion_rate = (enhanced_rows / total_rows) * 100 if total_rows > 0 else 0
        
        logger.info(f"📈 Enhancement completion rate: {completion_rate:.1f}% ({enhanced_rows}/{total_rows} rows)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing CSV: {str(e)}")
        return False

def generate_completion_report(input_file: str) -> None:
    """Generate a detailed completion report."""
    try:
        df = pd.read_csv(input_file)
        
        logger.info("\n📊 Detailed Completion Report:")
        logger.info("=" * 50)
        
        # Overall stats
        total_rows = len(df)
        enhanced_rows = 0
        partial_rows = 0
        failed_rows = 0
        
        # Category breakdown
        categories = {}
        
        for idx, row in df.iterrows():
            category = str(row.get('Main Category', 'Unknown')).strip()
            keyword = str(row.get('keyword', '')).strip()
            knowledge = str(row.get('knowledge point', '')).strip()
            source = str(row.get('knowledge point source', '')).strip()
            
            # Count by category
            if category not in categories:
                categories[category] = {'total': 0, 'enhanced': 0, 'partial': 0, 'failed': 0}
            categories[category]['total'] += 1
            
            # Determine completion status
            has_keyword = keyword and keyword != '' and keyword != 'nan'
            has_knowledge = knowledge and knowledge != '' and knowledge != 'nan'
            has_source = source and source != '' and source != 'nan'
            
            if has_keyword and has_knowledge and has_source:
                enhanced_rows += 1
                categories[category]['enhanced'] += 1
            elif has_keyword or has_knowledge or has_source:
                partial_rows += 1
                categories[category]['partial'] += 1
            else:
                failed_rows += 1
                categories[category]['failed'] += 1
        
        # Print overall stats
        logger.info(f"Total rows: {total_rows}")
        logger.info(f"Fully enhanced: {enhanced_rows} ({enhanced_rows/total_rows*100:.1f}%)")
        logger.info(f"Partially enhanced: {partial_rows} ({partial_rows/total_rows*100:.1f}%)")
        logger.info(f"Failed/Empty: {failed_rows} ({failed_rows/total_rows*100:.1f}%)")
        
        # Print category breakdown
        logger.info(f"\n📋 By Category:")
        for category, stats in categories.items():
            if stats['total'] > 0:
                success_rate = (stats['enhanced'] / stats['total']) * 100
                logger.info(f"   {category}: {stats['enhanced']}/{stats['total']} ({success_rate:.1f}%)")
        
        # Identify problematic rows
        logger.info(f"\n❌ Rows needing attention:")
        problem_count = 0
        for idx, row in df.iterrows():
            keyword = str(row.get('keyword', '')).strip()
            knowledge = str(row.get('knowledge point', '')).strip()
            korean_term = str(row.get('Keyword/Concept', '')).strip()
            
            if not keyword or keyword == '' or keyword == 'nan':
                if problem_count < 10:  # Show first 10 problems
                    logger.info(f"   Row {idx+2}: '{korean_term}' - No keyword generated")
                problem_count += 1
        
        if problem_count > 10:
            logger.info(f"   ... and {problem_count - 10} more rows")
        
    except Exception as e:
        logger.error(f"❌ Error generating report: {str(e)}")

def suggest_improvements(input_file: str) -> None:
    """Suggest improvements for the dataset."""
    logger.info(f"\n💡 Suggestions for improvement:")
    logger.info("=" * 40)
    
    try:
        df = pd.read_csv(input_file)
        issues = analyze_csv_issues(df)
        
        if issues['empty_enhancement_rows'] > 0:
            logger.info(f"1. Re-run enhancement for {issues['empty_enhancement_rows']} failed rows:")
            logger.info(f"   python vqa_enhancement.py VQA.csv -s <start_row> -m <batch_size>")
        
        if issues['missing_keywords'] > issues['empty_enhancement_rows']:
            logger.info(f"2. Use no-LLM version for remaining {issues['missing_keywords']} rows:")
            logger.info(f"   python vqa_enhancement_no_llm.py VQA.csv")
        
        if issues['malformed_urls'] > 0:
            logger.info(f"3. Fix {issues['malformed_urls']} malformed URLs manually")
        
        logger.info(f"4. Consider using multi-GPU for faster processing:")
        logger.info(f"   CUDA_VISIBLE_DEVICES=0,1,2,3 python vqa_enhancement.py VQA.csv")
        
    except Exception as e:
        logger.error(f"❌ Error generating suggestions: {str(e)}")

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python fix_csv_formatting.py <input_csv> [output_csv]")
        return 1
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else input_file.replace('.csv', '_fixed.csv')
    
    logger.info("🔧 VQA CSV Formatting Fix Tool")
    logger.info("=" * 40)
    
    # Generate completion report
    generate_completion_report(input_file)
    
    # Fix formatting issues
    if fix_csv_formatting(input_file, output_file):
        logger.info(f"✅ CSV formatting fixed successfully!")
        logger.info(f"📁 Fixed file saved as: {output_file}")
    else:
        logger.error("❌ Failed to fix CSV formatting")
        return 1
    
    # Suggest improvements
    suggest_improvements(output_file)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
