"""
Test script for Wikipedia + LLM generation approach.
Tests the new logic: Wikipedia first, then LLM generation with "generated" source.
"""

import logging
import pandas as pd
from vqa_enhancement import VQAEnhancer

logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create test data with different scenarios for Wikipedia + LLM generation."""
    test_data = [
        {
            'Question': 'What is this traditional Korean palace?',
            'Keyword/Concept': 'Gyeongbokgung Palace',
            'knowledge point': '',
            'knowledge point source': ''
        },
        {
            'Question': 'Which Korean meme phrase means "Who\'s on first?"',
            'Keyword/Concept': '1루수가 누구야',
            'knowledge point': '',
            'knowledge point source': ''
        },
        {
            'Question': 'What is this Korean cultural concept about quick service?',
            'Keyword/Concept': '빨리빨리 문화',
            'knowledge point': '',
            'knowledge point source': ''
        },
        {
            'Question': 'What does this Korean term for emotional bonds mean?',
            'Keyword/Concept': '정(情) 나누기',
            'knowledge point': '',
            'knowledge point source': ''
        },
        {
            'Question': 'What is this Korean traditional heating system?',
            'Keyword/Concept': '온돌',
            'knowledge point': '',
            'knowledge point source': ''
        }
    ]
    
    return pd.DataFrame(test_data)

def test_wikipedia_llm_generation():
    """Test the Wikipedia + LLM generation approach."""
    
    logger.info("🧪 Testing Wikipedia + LLM Generation Approach")
    logger.info("=" * 60)
    
    # Create test data
    test_df = create_test_data()
    
    # Save test data
    test_file = "test_wikipedia_llm_data.csv"
    test_df.to_csv(test_file, index=False)
    logger.info(f"Created test data: {test_file}")
    
    # Initialize enhancer
    enhancer = VQAEnhancer()
    
    try:
        # Initialize components
        logger.info("🚀 Initializing system...")
        if not enhancer.initialize():
            logger.error("❌ System initialization failed")
            return False
        
        logger.info("✅ System initialized successfully")
        
        # Test each row to see the logic in action
        logger.info("\n🔍 Testing each scenario:")
        logger.info("=" * 50)
        
        for idx, row in test_df.iterrows():
            logger.info(f"\n📋 Test Case {idx + 1}:")
            logger.info(f"   Question: {row['Question']}")
            logger.info(f"   Korean Term: {row['Keyword/Concept']}")
            
            # Process the row
            result = enhancer._process_row(row, idx)
            
            if result:
                logger.info(f"   ✅ Result: {result['keyword']}")
                logger.info(f"   📄 Knowledge: {result['knowledge_point'][:100]}...")
                logger.info(f"   🔗 Source: {result['source_url']}")
                
                # Determine which method was used
                if result['source_url'] == "generated":
                    logger.info(f"   🤖 Method: LLM Generated")
                elif "wikipedia.org" in result['source_url']:
                    logger.info(f"   📚 Method: Wikipedia Search")
                else:
                    logger.info(f"   📄 Method: Other")
            else:
                logger.info(f"   ❌ No result")
        
        logger.info(f"\n📊 Final Statistics:")
        enhancer._print_statistics()
        
        return True
        
    except Exception as e:
        logger.error(f"💥 Test failed: {str(e)}")
        return False
    finally:
        enhancer.cleanup()

def test_llm_generation_only():
    """Test just the LLM generation method."""
    
    logger.info("\n🤖 Testing LLM Generation Only")
    logger.info("=" * 40)
    
    enhancer = VQAEnhancer()
    
    try:
        # Initialize just the LLM
        if not enhancer.llm_handler.initialize_model():
            logger.error("❌ LLM initialization failed")
            return False
        
        # Test LLM generation
        test_cases = [
            ("What is this Korean cultural concept?", "빨리빨리 문화"),
            ("What does this Korean term mean?", "정(情) 나누기"),
            ("What is this Korean meme phrase?", "1루수가 누구야")
        ]
        
        for question, korean_term in test_cases:
            logger.info(f"\n🧠 Testing LLM generation:")
            logger.info(f"   Question: {question}")
            logger.info(f"   Korean Term: {korean_term}")
            
            knowledge = enhancer._generate_knowledge_with_llm(question, korean_term)
            
            if knowledge:
                logger.info(f"   ✅ Generated: {knowledge[:100]}...")
            else:
                logger.info(f"   ❌ Generation failed")
        
        return True
        
    except Exception as e:
        logger.error(f"💥 LLM test failed: {str(e)}")
        return False
    finally:
        enhancer.cleanup()

def main():
    """Run the Wikipedia + LLM generation tests."""
    logger.info("🚀 Wikipedia + LLM Generation Testing Suite")
    logger.info("=" * 60)
    
    # Test 1: Full Wikipedia + LLM approach
    success1 = test_wikipedia_llm_generation()
    
    # Test 2: LLM generation only
    success2 = test_llm_generation_only()
    
    if success1 and success2:
        logger.info("\n🎉 All tests completed successfully!")
        logger.info("✅ The system is ready to process VQA_new.csv")
        logger.info("🚀 Run: python vqa_enhancement.py VQA_new.csv --test")
    else:
        logger.info("\n❌ Some tests failed")
        logger.info("⚠️  Check the implementation before running on full dataset")

if __name__ == "__main__":
    main()
