2025-06-15 23:16:57,862 - INFO - 🚀 Starting VQA Enhancement System Test
2025-06-15 23:16:57,862 - INFO - 📡 Step 1/3: Testing Wikipedia connection...
2025-06-15 23:16:58,745 - INFO - ✅ Wikipedia connection successful
2025-06-15 23:16:58,746 - INFO - 🔍 Step 2/3: Initializing keyword extractor...
2025-06-15 23:16:58,746 - INFO - ✅ Keyword extractor ready
2025-06-15 23:16:58,747 - INFO - 🧪 Step 3/3: Testing components...
2025-06-15 23:16:58,747 - INFO - ✅ All components initialized successfully
2025-06-15 23:16:58,761 - INFO - 📋 Processing 10 sample rows from VQA.csv
2025-06-15 23:16:58,761 - INFO - 🔄 Processing row 1/10 (10.0% complete)
2025-06-15 23:16:58,762 - INFO -    📝 Korean term: 제주 돌집
2025-06-15 23:16:58,762 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '제주 돌집'...
2025-06-15 23:16:58,762 - INFO -      🧠 Starting LLM keyword generation for '제주 돌집'
2025-06-15 23:16:58,762 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:16:58,763 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:16:58,763 - INFO -      ⏳ Generating response...
2025-06-15 23:16:59,898 - INFO -      ⏱️  LLM inference completed in 1.50 seconds
2025-06-15 23:16:59,898 - INFO -      📤 Generated 23 characters of response
2025-06-15 23:16:59,898 - INFO -      ✅ LLM attempt 1 generated: Title: Jeju Stone House...
2025-06-15 23:16:59,899 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:16:59,899 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:16:59,899 - INFO -      🔍 Extracting keyword from response: Title: Jeju Stone House...
2025-06-15 23:16:59,899 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:16:59,899 - INFO -      🔍 Extracting keyword from response: Title: Jeju Stone House...
2025-06-15 23:16:59,901 - INFO -      📝 Raw keyword extracted: 'Jeju Stone House'
2025-06-15 23:16:59,901 - INFO -      🎉 Successfully extracted valid keyword: 'Jeju Stone House'
2025-06-15 23:16:59,901 - INFO -      📝 Raw keyword extracted: 'Jeju Stone House'
2025-06-15 23:16:59,901 - INFO -      🎉 Successfully extracted valid keyword: 'Jeju Stone House'
2025-06-15 23:16:59,902 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Jeju Stone House'...
2025-06-15 23:16:59,902 - INFO -      🔍 Searching Wikipedia for: 'Jeju Stone House'
2025-06-15 23:16:59,902 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:01,928 - INFO -        ✅ Auto-suggest found: 'Jeju City'
2025-06-15 23:17:01,929 - INFO -      ✅ Found page: 'Jeju City'
2025-06-15 23:17:01,929 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:02,533 - INFO -      📏 Extracted 321 characters of content
2025-06-15 23:17:02,533 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:02,533 - INFO -      🎉 Successfully retrieved knowledge from: Jeju City
2025-06-15 23:17:02,534 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Jeju_City
2025-06-15 23:17:02,534 - INFO -    🎉 Row 1: Successfully enhanced! '제주 돌집' → 'Jeju Stone House'
2025-06-15 23:17:02,534 - INFO -    📄 Knowledge preview: Jeju City (Korean: 제주시, romanized: Jeju-si; Korean pronunciation: ) is the capit...
2025-06-15 23:17:02,534 - INFO -    ✅ Row 1 enhanced successfully with keyword: Jeju Stone House (3.8s)
2025-06-15 23:17:02,535 - INFO - 🔄 Processing row 2/10 (20.0% complete, ETA: 30s)
2025-06-15 23:17:02,535 - INFO -    📝 Korean term: 월정교
2025-06-15 23:17:02,535 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '월정교'...
2025-06-15 23:17:02,536 - INFO -      🧠 Starting LLM keyword generation for '월정교'
2025-06-15 23:17:02,536 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:02,536 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:02,537 - INFO -      ⏳ Generating response...
2025-06-15 23:17:03,290 - INFO -      ⏱️  LLM inference completed in 2.96 seconds
2025-06-15 23:17:03,290 - INFO -      📤 Generated 18 characters of response
2025-06-15 23:17:03,290 - INFO -      ✅ LLM attempt 1 generated: Title: Woljeonggyo...
2025-06-15 23:17:03,291 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:03,291 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:03,291 - INFO -      🔍 Extracting keyword from response: Title: Woljeonggyo...
2025-06-15 23:17:03,291 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:03,291 - INFO -      🔍 Extracting keyword from response: Title: Woljeonggyo...
2025-06-15 23:17:03,291 - INFO -      📝 Raw keyword extracted: 'Woljeonggyo'
2025-06-15 23:17:03,291 - INFO -      🎉 Successfully extracted valid keyword: 'Woljeonggyo'
2025-06-15 23:17:03,291 - INFO -      📝 Raw keyword extracted: 'Woljeonggyo'
2025-06-15 23:17:03,292 - INFO -      🎉 Successfully extracted valid keyword: 'Woljeonggyo'
2025-06-15 23:17:03,292 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Woljeonggyo'...
2025-06-15 23:17:03,292 - INFO -      🔍 Searching Wikipedia for: 'Woljeonggyo'
2025-06-15 23:17:03,292 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:03,862 - INFO -        ✅ Exact match found: 'Woljeonggyo'
2025-06-15 23:17:03,862 - INFO -      ✅ Found page: 'Woljeonggyo'
2025-06-15 23:17:03,862 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:04,542 - INFO -      📏 Extracted 113 characters of content
2025-06-15 23:17:04,542 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:04,543 - INFO -      🎉 Successfully retrieved knowledge from: Woljeonggyo
2025-06-15 23:17:04,543 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Woljeonggyo
2025-06-15 23:17:04,543 - INFO -    🎉 Row 2: Successfully enhanced! '월정교' → 'Woljeonggyo'
2025-06-15 23:17:04,543 - INFO -    📄 Knowledge preview: Woljeonggyo (Korean: 월정교; Hanja: 月精橋) is a covered bridge in Gyeongju, South Kor...
2025-06-15 23:17:04,543 - INFO -    ✅ Row 2 enhanced successfully with keyword: Woljeonggyo (2.0s)
2025-06-15 23:17:04,544 - INFO - 🔄 Processing row 3/10 (30.0% complete, ETA: 20s)
2025-06-15 23:17:04,544 - INFO -    📝 Korean term: 운현궁
2025-06-15 23:17:04,545 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '운현궁'...
2025-06-15 23:17:04,545 - INFO -      🧠 Starting LLM keyword generation for '운현궁'
2025-06-15 23:17:04,545 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:04,546 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:04,546 - INFO -      ⏳ Generating response...
2025-06-15 23:17:05,582 - INFO -      ⏱️  LLM inference completed in 1.62 seconds
2025-06-15 23:17:05,582 - INFO -      📤 Generated 18 characters of response
2025-06-15 23:17:05,582 - INFO -      ✅ LLM attempt 1 generated: Unhyeongung Palace...
2025-06-15 23:17:05,583 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:05,583 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:05,583 - INFO -      🔍 Extracting keyword from response: Unhyeongung Palace...
2025-06-15 23:17:05,583 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:05,583 - INFO -      🔍 Extracting keyword from response: Unhyeongung Palace...
2025-06-15 23:17:05,586 - INFO -      📝 Raw keyword extracted: 'Unhyeongung Palace'
2025-06-15 23:17:05,586 - INFO -      🎉 Successfully extracted valid keyword: 'Unhyeongung Palace'
2025-06-15 23:17:05,587 - INFO -      📝 Raw keyword extracted: 'Unhyeongung Palace'
2025-06-15 23:17:05,587 - INFO -      🎉 Successfully extracted valid keyword: 'Unhyeongung Palace'
2025-06-15 23:17:05,587 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Unhyeongung Palace'...
2025-06-15 23:17:05,587 - INFO -      🔍 Searching Wikipedia for: 'Unhyeongung Palace'
2025-06-15 23:17:05,587 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:07,739 - INFO -        ✅ Auto-suggest found: 'Unhyeongung'
2025-06-15 23:17:07,739 - INFO -      ✅ Found page: 'Unhyeongung'
2025-06-15 23:17:07,739 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:08,439 - INFO -      📏 Extracted 401 characters of content
2025-06-15 23:17:08,439 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:08,439 - INFO -      🎉 Successfully retrieved knowledge from: Unhyeongung
2025-06-15 23:17:08,440 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Unhyeongung
2025-06-15 23:17:08,440 - INFO -    🎉 Row 3: Successfully enhanced! '운현궁' → 'Unhyeongung Palace'
2025-06-15 23:17:08,440 - INFO -    📄 Knowledge preview: Unhyeongung (Korean: 운현궁), also known as Unhyeongung Royal Residence, is a forme...
2025-06-15 23:17:08,440 - INFO -    ✅ Row 3 enhanced successfully with keyword: Unhyeongung Palace (3.9s)
2025-06-15 23:17:08,440 - INFO - 🔄 Processing row 4/10 (40.0% complete, ETA: 19s)
2025-06-15 23:17:08,441 - INFO -    📝 Korean term: 명동
2025-06-15 23:17:08,441 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '명동'...
2025-06-15 23:17:08,441 - INFO -      🧠 Starting LLM keyword generation for '명동'
2025-06-15 23:17:08,441 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:08,441 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:08,441 - INFO -      ⏳ Generating response...
2025-06-15 23:17:09,509 - INFO -      ⏱️  LLM inference completed in 2.98 seconds
2025-06-15 23:17:09,509 - INFO -      📤 Generated 9 characters of response
2025-06-15 23:17:09,510 - INFO -      ✅ LLM attempt 1 generated: Title: 명동...
2025-06-15 23:17:09,510 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:09,510 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:09,510 - INFO -      🔍 Extracting keyword from response: Title: 명동...
2025-06-15 23:17:09,510 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:09,510 - INFO -      🔍 Extracting keyword from response: Title: 명동...
2025-06-15 23:17:09,510 - INFO -      📝 Raw keyword extracted: '명동'
2025-06-15 23:17:09,511 - WARNING -      ❌ Extracted keyword '명동' failed validation
2025-06-15 23:17:09,511 - WARNING -    ❌ Row 3: Failed to extract valid keyword
2025-06-15 23:17:09,511 - INFO -    ❌ Row 4 enhancement failed (1.1s)
2025-06-15 23:17:09,512 - INFO - 🔄 Processing row 5/10 (50.0% complete, ETA: 13s)
2025-06-15 23:17:09,512 - INFO -    📝 Korean term: 남산타워
2025-06-15 23:17:09,512 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '남산타워'...
2025-06-15 23:17:09,512 - INFO -      🧠 Starting LLM keyword generation for '남산타워'
2025-06-15 23:17:09,512 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:09,512 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:09,513 - INFO -      ⏳ Generating response...
2025-06-15 23:17:10,438 - INFO -      ⏱️  LLM inference completed in 2.17 seconds
2025-06-15 23:17:10,439 - INFO -      📤 Generated 20 characters of response
2025-06-15 23:17:10,439 - INFO -      ✅ LLM attempt 1 generated: Title: N Seoul Tower...
2025-06-15 23:17:10,439 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:10,440 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:10,440 - INFO -      🔍 Extracting keyword from response: Title: N Seoul Tower...
2025-06-15 23:17:10,440 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:10,440 - INFO -      🔍 Extracting keyword from response: Title: N Seoul Tower...
2025-06-15 23:17:10,440 - INFO -      📝 Raw keyword extracted: 'N Seoul Tower'
2025-06-15 23:17:10,441 - INFO -      🎉 Successfully extracted valid keyword: 'N Seoul Tower'
2025-06-15 23:17:10,441 - INFO -      📝 Raw keyword extracted: 'N Seoul Tower'
2025-06-15 23:17:10,441 - INFO -      🎉 Successfully extracted valid keyword: 'N Seoul Tower'
2025-06-15 23:17:10,441 - INFO -    📚 Step 3/3: Searching Wikipedia for 'N Seoul Tower'...
2025-06-15 23:17:10,441 - INFO -      🔍 Searching Wikipedia for: 'N Seoul Tower'
2025-06-15 23:17:10,442 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:11,037 - INFO -        ✅ Exact match found: 'N Seoul Tower'
2025-06-15 23:17:11,037 - INFO -      ✅ Found page: 'N Seoul Tower'
2025-06-15 23:17:11,038 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:11,650 - INFO -      📏 Extracted 79 characters of content
2025-06-15 23:17:11,650 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:11,650 - INFO -      🎉 Successfully retrieved knowledge from: N Seoul Tower
2025-06-15 23:17:11,651 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/N_Seoul_Tower
2025-06-15 23:17:11,651 - INFO -    🎉 Row 5: Successfully enhanced! '남산타워' → 'N Seoul Tower'
2025-06-15 23:17:11,651 - INFO -    📄 Knowledge preview: The N Seoul Tower (Korean: N 서울타워), officially the YTN Seoul Tower and a. k. a....
2025-06-15 23:17:11,651 - INFO -    ✅ Row 5 enhanced successfully with keyword: N Seoul Tower (2.1s)
2025-06-15 23:17:11,651 - INFO - 💓 Heartbeat: 5 rows processed, avg 2.6s/row
2025-06-15 23:17:11,652 - INFO - 🔄 Processing row 6/10 (60.0% complete, ETA: 10s)
2025-06-15 23:17:11,652 - INFO -    📝 Korean term: 신라대종
2025-06-15 23:17:11,653 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '신라대종'...
2025-06-15 23:17:11,653 - INFO -      🧠 Starting LLM keyword generation for '신라대종'
2025-06-15 23:17:11,653 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:11,653 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:11,653 - INFO -      ⏳ Generating response...
2025-06-15 23:17:13,487 - INFO -      ⏱️  LLM inference completed in 1.15 seconds
2025-06-15 23:17:13,487 - INFO -      📤 Generated 11 characters of response
2025-06-15 23:17:13,488 - INFO -      ✅ LLM attempt 1 generated: Emille Bell...
2025-06-15 23:17:13,488 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:13,488 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:13,488 - INFO -      🔍 Extracting keyword from response: Emille Bell...
2025-06-15 23:17:13,488 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:13,488 - INFO -      🔍 Extracting keyword from response: Emille Bell...
2025-06-15 23:17:13,489 - INFO -      📝 Raw keyword extracted: 'Emille Bell'
2025-06-15 23:17:13,489 - INFO -      🎉 Successfully extracted valid keyword: 'Emille Bell'
2025-06-15 23:17:13,490 - INFO -      📝 Raw keyword extracted: 'Emille Bell'
2025-06-15 23:17:13,490 - INFO -      🎉 Successfully extracted valid keyword: 'Emille Bell'
2025-06-15 23:17:13,490 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Emille Bell'...
2025-06-15 23:17:13,490 - INFO -      🔍 Searching Wikipedia for: 'Emille Bell'
2025-06-15 23:17:13,491 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:14,740 - INFO -        ✅ Exact match found: 'Bell of King Seongdeok'
2025-06-15 23:17:14,740 - INFO -      ✅ Found page: 'Bell of King Seongdeok'
2025-06-15 23:17:14,741 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:15,321 - INFO -      📏 Extracted 308 characters of content
2025-06-15 23:17:15,321 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:15,321 - INFO -      🎉 Successfully retrieved knowledge from: Bell of King Seongdeok
2025-06-15 23:17:15,322 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Bell_of_King_Seongdeok
2025-06-15 23:17:15,322 - INFO -    🎉 Row 6: Successfully enhanced! '신라대종' → 'Emille Bell'
2025-06-15 23:17:15,322 - INFO -    📄 Knowledge preview: The Sacred Bell of Great King Seongdeok (Korean: 성덕대왕신종; Hanja: 聖德大王神鍾) is the l...
2025-06-15 23:17:15,322 - INFO -    ✅ Row 6 enhanced successfully with keyword: Emille Bell (3.7s)
2025-06-15 23:17:15,323 - INFO - 🔄 Processing row 7/10 (70.0% complete, ETA: 8s)
2025-06-15 23:17:15,323 - INFO -    📝 Korean term: 고려대학교
2025-06-15 23:17:15,323 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '고려대학교'...
2025-06-15 23:17:15,323 - INFO -      🧠 Starting LLM keyword generation for '고려대학교'
2025-06-15 23:17:15,323 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:15,323 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:15,324 - INFO -      ⏳ Generating response...
2025-06-15 23:17:17,275 - INFO -      ⏱️  LLM inference completed in 1.01 seconds
2025-06-15 23:17:17,275 - INFO -      📤 Generated 16 characters of response
2025-06-15 23:17:17,275 - INFO -      ✅ LLM attempt 1 generated: Korea University...
2025-06-15 23:17:17,276 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:17,276 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:17,276 - INFO -      🔍 Extracting keyword from response: Korea University...
2025-06-15 23:17:17,276 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:17,277 - INFO -      🔍 Extracting keyword from response: Korea University...
2025-06-15 23:17:17,277 - INFO -      📝 Raw keyword extracted: 'Korea University'
2025-06-15 23:17:17,278 - INFO -      🎉 Successfully extracted valid keyword: 'Korea University'
2025-06-15 23:17:17,278 - INFO -      📝 Raw keyword extracted: 'Korea University'
2025-06-15 23:17:17,279 - INFO -      🎉 Successfully extracted valid keyword: 'Korea University'
2025-06-15 23:17:17,279 - INFO -    📚 Step 3/3: Searching Wikipedia for 'Korea University'...
2025-06-15 23:17:17,280 - INFO -      🔍 Searching Wikipedia for: 'Korea University'
2025-06-15 23:17:17,280 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:17,963 - INFO -        ✅ Exact match found: 'Korea University'
2025-06-15 23:17:17,963 - INFO -      ✅ Found page: 'Korea University'
2025-06-15 23:17:17,964 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:18,977 - INFO -      📏 Extracted 342 characters of content
2025-06-15 23:17:18,978 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:18,978 - INFO -      🎉 Successfully retrieved knowledge from: Korea University
2025-06-15 23:17:18,978 - INFO -      🔗 Source URL: https://en.wikipedia.org/wiki/Korea_University
2025-06-15 23:17:18,979 - INFO -    🎉 Row 7: Successfully enhanced! '고려대학교' → 'Korea University'
2025-06-15 23:17:18,979 - INFO -    📄 Knowledge preview: Korea University (KU, Korean: 고려대학교; RR: Goryeo Daehakgyo) is a private research...
2025-06-15 23:17:18,979 - INFO -    ✅ Row 7 enhanced successfully with keyword: Korea University (3.7s)
2025-06-15 23:17:18,980 - INFO - 🔄 Processing row 8/10 (80.0% complete, ETA: 6s)
2025-06-15 23:17:18,980 - INFO -    📝 Korean term: 한강다리
2025-06-15 23:17:18,980 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '한강다리'...
2025-06-15 23:17:18,980 - INFO -      🧠 Starting LLM keyword generation for '한강다리'
2025-06-15 23:17:18,980 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:18,980 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:18,980 - INFO -      ⏳ Generating response...
2025-06-15 23:17:20,782 - INFO -      ⏱️  LLM inference completed in 2.67 seconds
2025-06-15 23:17:20,782 - INFO -      📤 Generated 11 characters of response
2025-06-15 23:17:20,782 - INFO -      ✅ LLM attempt 1 generated: Title: 한강다리...
2025-06-15 23:17:20,783 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:20,783 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:20,783 - INFO -      🔍 Extracting keyword from response: Title: 한강다리...
2025-06-15 23:17:20,784 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:20,784 - INFO -      🔍 Extracting keyword from response: Title: 한강다리...
2025-06-15 23:17:20,785 - INFO -      📝 Raw keyword extracted: '한강다리'
2025-06-15 23:17:20,786 - WARNING -      ❌ Extracted keyword '한강다리' failed validation
2025-06-15 23:17:20,786 - WARNING -    ❌ Row 7: Failed to extract valid keyword
2025-06-15 23:17:20,786 - INFO -    ❌ Row 8 enhancement failed (1.8s)
2025-06-15 23:17:20,787 - INFO - 🔄 Processing row 9/10 (90.0% complete, ETA: 3s)
2025-06-15 23:17:20,787 - INFO -    📝 Korean term: DDP
2025-06-15 23:17:20,787 - INFO -    🤖 Step 1/3: Generating keywords using LLM for 'DDP'...
2025-06-15 23:17:20,787 - INFO -      🧠 Starting LLM keyword generation for 'DDP'
2025-06-15 23:17:20,788 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:20,788 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:20,788 - INFO -      ⏳ Generating response...
2025-06-15 23:17:21,538 - INFO -      ⏱️  LLM inference completed in 1.05 seconds
2025-06-15 23:17:21,538 - INFO -      📤 Generated 10 characters of response
2025-06-15 23:17:21,538 - INFO -      ✅ LLM attempt 1 generated: Title: DDP...
2025-06-15 23:17:21,539 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:21,539 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:21,539 - INFO -      🔍 Extracting keyword from response: Title: DDP...
2025-06-15 23:17:21,539 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:21,540 - INFO -      🔍 Extracting keyword from response: Title: DDP...
2025-06-15 23:17:21,540 - INFO -      📝 Raw keyword extracted: 'DDP'
2025-06-15 23:17:21,540 - INFO -      🎉 Successfully extracted valid keyword: 'DDP'
2025-06-15 23:17:21,540 - INFO -      📝 Raw keyword extracted: 'DDP'
2025-06-15 23:17:21,541 - INFO -      🎉 Successfully extracted valid keyword: 'DDP'
2025-06-15 23:17:21,541 - INFO -    📚 Step 3/3: Searching Wikipedia for 'DDP'...
2025-06-15 23:17:21,541 - INFO -      🔍 Searching Wikipedia for: 'DDP'
2025-06-15 23:17:21,542 - INFO -      📄 Step 1: Finding Wikipedia page...
2025-06-15 23:17:23,095 - INFO -        🔀 Disambiguation found for 'DDP', trying: Delivered Duty Paid
2025-06-15 23:17:24,424 - INFO -        ✅ Disambiguation resolved: 'Incoterms'
2025-06-15 23:17:24,425 - INFO -      ✅ Found page: 'Incoterms'
2025-06-15 23:17:24,425 - INFO -      📝 Step 2: Extracting content from page...
2025-06-15 23:17:25,206 - INFO -      📏 Extracted 533 characters of content
2025-06-15 23:17:25,206 - INFO -      🏛️  Step 3: Validating Korean cultural relevance...
2025-06-15 23:17:25,207 - WARNING -      ❌ Content not relevant to Korean culture: Incoterms
2025-06-15 23:17:25,207 - INFO -    ❌ Row 9 enhancement failed (4.4s)
2025-06-15 23:17:25,208 - INFO - 🔄 Processing row 10/10 (100.0% complete, ETA: 0s)
2025-06-15 23:17:25,208 - INFO -    📝 Korean term: 탑골공원
2025-06-15 23:17:25,208 - INFO -    🤖 Step 1/3: Generating keywords using LLM for '탑골공원'...
2025-06-15 23:17:25,208 - INFO -      🧠 Starting LLM keyword generation for '탑골공원'
2025-06-15 23:17:25,209 - INFO -      🎯 LLM Attempt 1/3 (temperature: 0.7)
2025-06-15 23:17:25,209 - INFO -      📝 Using prompt strategy: attempt_1
2025-06-15 23:17:25,209 - INFO -      ⏳ Generating response...
2025-06-15 23:17:26,796 - INFO -      ⏱️  LLM inference completed in 2.14 seconds
2025-06-15 23:17:26,796 - INFO -      📤 Generated 11 characters of response
2025-06-15 23:17:26,796 - INFO -      ✅ LLM attempt 1 generated: Title: 탑골공원...
2025-06-15 23:17:26,797 - INFO -      🎉 Found valid response on attempt 1, stopping iteration
2025-06-15 23:17:26,797 - INFO -    🔍 Step 2/3: Extracting clean keyword from LLM response...
2025-06-15 23:17:26,797 - INFO -      🔍 Extracting keyword from response: Title: 탑골공원...
2025-06-15 23:17:26,797 - INFO -      🎯 Trying extraction patterns...
2025-06-15 23:17:26,797 - INFO -      🔍 Extracting keyword from response: Title: 탑골공원...
2025-06-15 23:17:26,797 - INFO -      📝 Raw keyword extracted: '탑골공원'
2025-06-15 23:17:26,798 - WARNING -      ❌ Extracted keyword '탑골공원' failed validation
2025-06-15 23:17:26,798 - WARNING -    ❌ Row 9: Failed to extract valid keyword
2025-06-15 23:17:26,798 - INFO -    ❌ Row 10 enhancement failed (1.6s)
2025-06-15 23:17:26,798 - INFO - === Enhancement Statistics ===
2025-06-15 23:17:26,798 - INFO - Total rows processed: 10
2025-06-15 23:17:26,798 - INFO - Successful enhancements: 6
2025-06-15 23:17:26,799 - INFO - Success rate: 60.0%
2025-06-15 23:17:26,799 - INFO - Total processing time: 28.0 seconds
2025-06-15 23:17:26,799 - INFO - Average time per row: 2.8 seconds
2025-06-15 23:17:26,799 - INFO - 🎉 Test completed!
