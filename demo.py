"""
Demo script for VQA Dataset Enhancement System
Demonstrates the system functionality without requiring the full LLM model.
"""

import pandas as pd
import logging
from keyword_extractor import KeywordExtractor
from wikipedia_handler import WikipediaHandler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def demo_keyword_extraction():
    """Demonstrate keyword extraction from simulated LLM responses."""
    print("=== Keyword Extraction Demo ===")
    
    extractor = KeywordExtractor()
    
    # Simulate LLM responses for Korean cultural terms
    simulated_responses = [
        ("제주 돌집", "Title: Jeju Stone House"),
        ("운현궁", "Unhyeongung Palace"),
        ("월정교", "Title: Woljeonggyo"),
        ("경복궁", "Gyeongbokgung"),
        ("불국사", "\"Bulguksa Temple\""),
        ("첨성대", "I recommend Cheomseongdae Observatory"),
        ("한옥마을", "Korean traditional village"),  # Should be rejected
        ("문화", "NONE"),  # Should be rejected
    ]
    
    print(f"{'Korean Term':<15} {'Simulated LLM Response':<35} {'Extracted Keyword':<20} {'Valid'}")
    print("-" * 85)
    
    for korean_term, llm_response in simulated_responses:
        keyword = extractor.extract_keyword(llm_response)
        valid = "✅" if keyword else "❌"
        keyword_display = keyword if keyword else "None"
        
        print(f"{korean_term:<15} {llm_response:<35} {keyword_display:<20} {valid}")
    
    print()

def demo_wikipedia_integration():
    """Demonstrate Wikipedia knowledge retrieval."""
    print("=== Wikipedia Integration Demo ===")
    
    handler = WikipediaHandler()
    
    # Test keywords that should work
    test_keywords = [
        "Gyeongbokgung",
        "Bulguksa",
        "Jeju Island", 
        "Cheomseongdae",
        "Unhyeongung"
    ]
    
    for keyword in test_keywords:
        print(f"\nSearching for: {keyword}")
        knowledge_point, source_url = handler.get_knowledge_point(keyword)
        
        if knowledge_point:
            print(f"✅ Found content:")
            print(f"   {knowledge_point[:150]}...")
            print(f"   Source: {source_url}")
        else:
            print(f"❌ No content found")
    
    print()

def demo_full_pipeline():
    """Demonstrate the full pipeline with sample data."""
    print("=== Full Pipeline Demo ===")
    
    extractor = KeywordExtractor()
    wikipedia = WikipediaHandler()
    
    # Sample VQA entries
    sample_data = [
        {
            "korean_term": "경복궁",
            "question": "What does the location and grand scale of this historic gate say about royal authority?",
            "simulated_llm": "Title: Gyeongbokgung"
        },
        {
            "korean_term": "불국사", 
            "question": "How do the stone terraces and pagodas demonstrate harmony between architecture and landscape?",
            "simulated_llm": "Bulguksa Temple"
        },
        {
            "korean_term": "제주 돌집",
            "question": "What environmental factors led to the use of volcanic basalt rock and thatched roofs?",
            "simulated_llm": "I suggest Jeju Stone House"
        }
    ]
    
    print(f"{'Korean Term':<12} {'Extracted Keyword':<18} {'Wikipedia Found':<15} {'Status'}")
    print("-" * 65)
    
    for item in sample_data:
        # Step 1: Extract keyword
        keyword = extractor.extract_keyword(item["simulated_llm"])
        
        if not keyword:
            print(f"{item['korean_term']:<12} {'None':<18} {'N/A':<15} ❌ No keyword")
            continue
        
        # Step 2: Get Wikipedia content
        knowledge_point, source_url = wikipedia.get_knowledge_point(keyword)
        
        if knowledge_point:
            status = "✅ Success"
            wiki_status = "Yes"
        else:
            status = "⚠️  No Wikipedia"
            wiki_status = "No"
        
        print(f"{item['korean_term']:<12} {keyword:<18} {wiki_status:<15} {status}")
    
    print()

def demo_csv_processing():
    """Demonstrate processing a few rows from the actual CSV."""
    print("=== CSV Processing Demo ===")
    
    try:
        # Load the CSV
        df = pd.read_csv("VQA.csv")
        
        # Process first 3 rows as demo
        sample_rows = df.head(3)
        
        extractor = KeywordExtractor()
        wikipedia = WikipediaHandler()
        
        print("Processing sample rows from VQA.csv:")
        print()
        
        for idx, row in sample_rows.iterrows():
            korean_term = row['Keyword/Concept']
            question = row['Question']
            
            print(f"Row {idx + 1}:")
            print(f"  Korean term: {korean_term}")
            print(f"  Question: {question[:80]}...")
            
            # Simulate what the LLM might generate
            # In real usage, this would come from the LLM
            simulated_responses = {
                "제주 돌집": "Title: Jeju Stone House",
                "월정교": "Woljeonggyo Bridge", 
                "운현궁": "Unhyeongung Palace"
            }
            
            simulated_llm = simulated_responses.get(korean_term, f"Title: {korean_term}")
            
            # Extract keyword
            keyword = extractor.extract_keyword(simulated_llm)
            
            if keyword:
                # Get Wikipedia content
                knowledge_point, source_url = wikipedia.get_knowledge_point(keyword)
                
                if knowledge_point:
                    print(f"  ✅ Enhanced with keyword: {keyword}")
                    print(f"     Knowledge: {knowledge_point[:100]}...")
                    print(f"     Source: {source_url}")
                else:
                    print(f"  ⚠️  Keyword found ({keyword}) but no Wikipedia content")
            else:
                print(f"  ❌ No valid keyword extracted from: {simulated_llm}")
            
            print()
            
    except FileNotFoundError:
        print("VQA.csv not found - skipping CSV demo")
        print("To run this demo, ensure VQA.csv is in the current directory")

def main():
    """Run all demos."""
    print("🎯 VQA Dataset Enhancement System Demo")
    print("=" * 50)
    print()
    
    # Run individual demos
    demo_keyword_extraction()
    demo_wikipedia_integration()
    demo_full_pipeline()
    demo_csv_processing()
    
    print("🎉 Demo completed!")
    print()
    print("Next steps:")
    print("1. Install transformers and torch for LLM support:")
    print("   pip install torch transformers accelerate")
    print("2. Run the full system:")
    print("   python vqa_enhancement.py VQA.csv --test")
    print("3. For production use:")
    print("   python vqa_enhancement.py VQA.csv")

if __name__ == "__main__":
    main()
