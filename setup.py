"""
Setup script for VQA Dataset Enhancement System
"""

import subprocess
import sys
import os
import torch

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_cuda():
    """Check CUDA availability."""
    if torch.cuda.is_available():
        print(f"✅ CUDA available: {torch.cuda.get_device_name(0)}")
        print(f"   CUDA version: {torch.version.cuda}")
        print(f"   Available memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        return True
    else:
        print("⚠️  CUDA not available - will use CPU mode")
        return False

def install_requirements():
    """Install required packages."""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def download_model_info():
    """Provide information about model download."""
    print("\n🤖 Model Information:")
    print("The system uses meta-llama/Llama-3.1-8B-Instruct")
    print("Model size: ~15GB")
    print("First run will download the model automatically")
    print("Ensure you have sufficient disk space and internet connection")
    
    # Check if user has Hugging Face token for gated models
    hf_token = os.environ.get("HF_TOKEN") or os.environ.get("HUGGING_FACE_HUB_TOKEN")
    if hf_token:
        print("✅ Hugging Face token detected")
    else:
        print("⚠️  No Hugging Face token detected")
        print("   If the model is gated, you may need to:")
        print("   1. Create account at https://huggingface.co")
        print("   2. Request access to meta-llama/Llama-3.1-8B-Instruct")
        print("   3. Set HF_TOKEN environment variable")

def run_tests():
    """Run system tests."""
    print("\n🧪 Running system tests...")
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ All tests passed")
            return True
        else:
            print("❌ Some tests failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def main():
    """Main setup function."""
    print("=== VQA Dataset Enhancement System Setup ===\n")
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Check CUDA
    cuda_available = check_cuda()
    
    # Install requirements
    if not install_requirements():
        return 1
    
    # Model information
    download_model_info()
    
    # Run tests
    print("\nWould you like to run system tests? (y/n): ", end="")
    try:
        response = input().lower().strip()
        if response in ['y', 'yes']:
            if not run_tests():
                print("\n⚠️  Some tests failed, but setup is complete")
                print("You can still use the system - test failures may be due to missing model or network issues")
    except KeyboardInterrupt:
        print("\nSkipping tests...")
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Ensure your VQA.csv file is in the current directory")
    print("2. Run: python vqa_enhancement.py VQA.csv --test")
    print("3. For full processing: python vqa_enhancement.py VQA.csv")
    
    if not cuda_available:
        print("\n💡 Performance tip:")
        print("For faster processing, consider using a GPU-enabled environment")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
