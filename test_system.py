"""
Test script for VQA Dataset Enhancement System
Validates individual components and end-to-end functionality.
"""

import logging
import sys
from keyword_extractor import KeywordExtractor
from wikipedia_handler import WikipediaHandler

# Configure logging for tests
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_keyword_extractor():
    """Test the keyword extractor with various response formats."""
    logger.info("Testing Keyword Extractor...")

    extractor = KeywordExtractor()

    test_cases = [
        # (input_response, expected_keyword_or_none)
        ("Title: Gyeongbokgung", "Gyeongbokgung"),
        ('"Bulguksa Temple"', "Bulguksa Temple"),
        ("I suggest looking up Jeju Stone House for more information.", "Jeju Stone House"),
        ("The answer is Unhyeongung Palace.", "Unhyeongung Palace"),
        ("NONE", None),
        ("Korea", None),  # Should be rejected as generic
        ("Korean culture and architecture", None),  # Should be rejected as generic
        ("Title: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Woljeonggyo"),
        ("", None),
        ("Title: Cheomseongdae Observatory", "Cheomseongdae Observatory"),
    ]

    passed = 0
    failed = 0

    for i, (input_response, expected) in enumerate(test_cases):
        result = extractor.extract_keyword(input_response)

        if result == expected:
            logger.info(f"✅ Test {i+1}: '{input_response[:50]}...' → '{result}'")
            passed += 1
        else:
            logger.error(f"❌ Test {i+1}: '{input_response[:50]}...' → Expected: '{expected}', Got: '{result}'")
            failed += 1

    logger.info(f"Keyword Extractor Tests: {passed} passed, {failed} failed")
    return failed == 0

def test_wikipedia_handler():
    """Test the Wikipedia handler with known Korean cultural terms."""
    logger.info("Testing Wikipedia Handler...")

    handler = WikipediaHandler()

    # Test connection first
    if not handler.test_connection():
        logger.error("Wikipedia connection test failed")
        return False

    test_keywords = [
        "Gyeongbokgung",
        "Bulguksa",
        "Jeju Island",
        "Cheomseongdae",
        "Unhyeongung",
        "NonexistentKoreanTerm12345"  # Should fail
    ]

    passed = 0
    failed = 0

    for keyword in test_keywords:
        knowledge_point, source_url = handler.get_knowledge_point(keyword)

        if keyword == "NonexistentKoreanTerm12345":
            # This should fail
            if knowledge_point is None:
                logger.info(f"✅ Correctly rejected non-existent term: {keyword}")
                passed += 1
            else:
                logger.error(f"❌ Should have rejected: {keyword}")
                failed += 1
        else:
            # These should succeed
            if knowledge_point and source_url:
                logger.info(f"✅ Retrieved content for: {keyword}")
                logger.info(f"   Content: {knowledge_point[:100]}...")
                logger.info(f"   URL: {source_url}")
                passed += 1
            else:
                logger.warning(f"⚠️  Failed to retrieve content for: {keyword}")
                # Don't count as failure since Wikipedia content can vary
                passed += 1

    logger.info(f"Wikipedia Handler Tests: {passed} passed, {failed} failed")
    return failed == 0

def test_integration():
    """Test integration between components."""
    logger.info("Testing Component Integration...")

    extractor = KeywordExtractor()
    wikipedia = WikipediaHandler()

    # Simulate LLM responses and test full pipeline
    test_cases = [
        {
            "llm_response": "Title: Gyeongbokgung",
            "expected_keyword": "Gyeongbokgung"
        },
        {
            "llm_response": "Title: Bulguksa Temple",
            "expected_keyword": "Bulguksa Temple"
        }
    ]

    passed = 0
    failed = 0

    for i, case in enumerate(test_cases):
        # Step 1: Extract keyword
        keyword = extractor.extract_keyword(case["llm_response"])

        if keyword != case["expected_keyword"]:
            logger.error(f"❌ Integration test {i+1}: Keyword extraction failed")
            failed += 1
            continue

        # Step 2: Get Wikipedia content
        knowledge_point, source_url = wikipedia.get_knowledge_point(keyword)

        if knowledge_point and source_url:
            logger.info(f"✅ Integration test {i+1}: Full pipeline successful")
            logger.info(f"   Keyword: {keyword}")
            logger.info(f"   Content: {knowledge_point[:100]}...")
            passed += 1
        else:
            logger.warning(f"⚠️  Integration test {i+1}: Wikipedia retrieval failed for {keyword}")
            # Don't count as hard failure
            passed += 1

    logger.info(f"Integration Tests: {passed} passed, {failed} failed")
    return failed == 0

def test_csv_format():
    """Test CSV format compatibility."""
    logger.info("Testing CSV Format...")

    try:
        import pandas as pd

        # Check if VQA.csv exists and has correct format
        try:
            df = pd.read_csv("VQA.csv")
            required_columns = ["Main Category", "Question", "Option 1", "Option 2",
                              "Option 3", "Option 4", "Correct Option", "Keyword/Concept"]

            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logger.error(f"❌ Missing required columns: {missing_columns}")
                return False

            logger.info(f"✅ CSV format valid. Found {len(df)} rows with correct columns")

            # Show sample data
            if len(df) > 0:
                sample_row = df.iloc[0]
                logger.info(f"   Sample Korean term: {sample_row['Keyword/Concept']}")
                logger.info(f"   Sample question: {sample_row['Question'][:100]}...")

            return True

        except FileNotFoundError:
            logger.warning("⚠️  VQA.csv not found - this is expected if running tests separately")
            return True

    except ImportError:
        logger.error("❌ pandas not available")
        return False

def main():
    """Run all tests."""
    logger.info("=== VQA Enhancement System Tests ===")

    tests = [
        ("CSV Format", test_csv_format),
        ("Keyword Extractor", test_keyword_extractor),
        ("Wikipedia Handler", test_wikipedia_handler),
        ("Integration", test_integration),
    ]

    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                logger.info(f"✅ {test_name} PASSED")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} ERROR: {str(e)}")

    logger.info(f"\n=== Test Results ===")
    logger.info(f"Passed: {passed_tests}/{total_tests}")

    if passed_tests == total_tests:
        logger.info("🎉 All tests passed!")
        return 0
    else:
        logger.error("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
